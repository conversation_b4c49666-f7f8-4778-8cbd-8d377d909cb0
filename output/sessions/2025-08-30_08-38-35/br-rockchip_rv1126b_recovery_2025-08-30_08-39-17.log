# 2025-08-30 08:39:17
2025-08-30T08:39:17 make: Entering directory '/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot'
2025-08-30T08:39:22 /usr/bin/make -j1  O=/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery HOSTCC="/usr/bin/gcc" HOSTCXX="/usr/bin/g++" syncconfig
2025-08-30T08:39:22 GEN     /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/Makefile
2025-08-30T08:39:22 [7m>>>   Finalizing host directory[27m
2025-08-30T08:39:22 [7m>>>   Finalizing target directory[27m
2025-08-30T08:39:22 mkdir -p /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/host/etc/meson
2025-08-30T08:39:22 sed -e "s%@TARGET_CFLAGS@%'-D_LARGEFILE_SOURCE', '-D_LARGEFILE64_SOURCE', '-D_FILE_OFFSET_BITS=64', '-O2', '-g0', '-D_FORTIFY_SOURCE=1'@PKG_TARGET_CFLAGS@%g" -e "s%@TARGET_LDFLAGS@%@PKG_TARGET_LDFLAGS@%g" -e "s%@TARGET_CXXFLAGS@%'-D_LARGEFILE_SOURCE', '-D_LARGEFILE64_SOURCE', '-D_FILE_OFFSET_BITS=64', '-O2', '-g0', '-D_FORTIFY_SOURCE=1'@PKG_TARGET_CXXFLAGS@%g" -e "s%@TARGET_FCFLAGS@%'-O2', '-g0'@PKG_TARGET_FCFLAGS@%g"         -e "s%@TARGET_CC@%/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/host/bin/aarch64-buildroot-linux-gnu-gcc%g" -e "s%@TARGET_CXX@%/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/host/bin/aarch64-buildroot-linux-gnu-g++%g" -e "s%@TARGET_AR@%/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/host/bin/aarch64-buildroot-linux-gnu-gcc-ar%g" -e "s%@TARGET_STRIP@%/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/host/bin/aarch64-buildroot-linux-gnu-strip%g"         -e "s%@TARGET_FC@%/bin/false%g" -e "s%@TARGET_ARCH@%aarch64%g" -e "s%@TARGET_CPU@%cortex-a53%g" -e "s%@TARGET_ENDIAN@%little%g" -e "s%@TARGET_FCFLAGS@%%g" -e "s%@TARGET_CFLAGS@%%g" -e "s%@TARGET_LDFLAGS@%%g" -e "s%@TARGET_CXXFLAGS@%%g" -e "s%@BR2_CMAKE@%/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/host/bin/cmake%g" -e "s%@PKGCONF_HOST_BINARY@%/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/host/bin/pkgconf%g" -e "s%@HOST_DIR@%/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/host%g" -e "s%@STAGING_DIR@%/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/host/aarch64-buildroot-linux-gnu/sysroot%g" -e "s%@STATIC@%false%g" /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/support/misc/cross-compilation.conf.in > /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/host/etc/meson/cross-compilation.conf.in
2025-08-30T08:39:22 sed         -e "s%@TARGET_CC@%/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/host/bin/aarch64-buildroot-linux-gnu-gcc%g" -e "s%@TARGET_CXX@%/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/host/bin/aarch64-buildroot-linux-gnu-g++%g" -e "s%@TARGET_AR@%/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/host/bin/aarch64-buildroot-linux-gnu-gcc-ar%g" -e "s%@TARGET_STRIP@%/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/host/bin/aarch64-buildroot-linux-gnu-strip%g"         -e "s%@TARGET_FC@%/bin/false%g" -e "s%@TARGET_ARCH@%aarch64%g" -e "s%@TARGET_CPU@%cortex-a53%g" -e "s%@TARGET_ENDIAN@%little%g" -e "s%@TARGET_FCFLAGS@%'-O2', '-g0'%g" -e "s%@TARGET_CFLAGS@%'-D_LARGEFILE_SOURCE', '-D_LARGEFILE64_SOURCE', '-D_FILE_OFFSET_BITS=64', '-O2', '-g0', '-D_FORTIFY_SOURCE=1'%g" -e "s%@TARGET_LDFLAGS@%%g" -e "s%@TARGET_CXXFLAGS@%'-D_LARGEFILE_SOURCE', '-D_LARGEFILE64_SOURCE', '-D_FILE_OFFSET_BITS=64', '-O2', '-g0', '-D_FORTIFY_SOURCE=1'%g" -e "s%@BR2_CMAKE@%/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/host/bin/cmake%g" -e "s%@PKGCONF_HOST_BINARY@%/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/host/bin/pkgconf%g" -e "s%@HOST_DIR@%/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/host%g" -e "s%@STAGING_DIR@%/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/host/aarch64-buildroot-linux-gnu/sysroot%g" -e "s%@STATIC@%false%g" /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/support/misc/cross-compilation.conf.in > /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/host/etc/meson/cross-compilation.conf
2025-08-30T08:39:22 /usr/bin/sed -i -e '/# GENERIC_SERIAL$/s~^.*#~::respawn:-/bin/sh # ttyS0::respawn:/sbin/getty -L ttyS0 115200 vt100 #~' /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/target/etc/inittab
2025-08-30T08:39:22 /usr/bin/sed -i -e '/^#.*-o remount,rw \/$/s~^#\+~~' /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/target/etc/inittab
2025-08-30T08:39:22 if grep -q CONFIG_ASH=y /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/build/busybox-1.36.1/.config; then grep -qsE '^/bin/ash$' /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/target/etc/shells || echo "/bin/ash" >> /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/target/etc/shells; fi
2025-08-30T08:39:22 if grep -q CONFIG_HUSH=y /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/build/busybox-1.36.1/.config; then grep -qsE '^/bin/hush$' /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/target/etc/shells || echo "/bin/hush" >> /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/target/etc/shells; fi
2025-08-30T08:39:22 mkdir -p /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/target/etc
2025-08-30T08:39:22 echo "rv1126b" > /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/target/etc/hostname
2025-08-30T08:39:22 /usr/bin/sed -i -e '$a \*********\trv1126b' -e '/^*********/d' /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/target/etc/hosts
2025-08-30T08:39:22 mkdir -p /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/target/etc
2025-08-30T08:39:22 echo "Welcome to RV1126B Buildroot" > /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/target/etc/issue
2025-08-30T08:39:22 /usr/bin/sed -i -e s,^root:[^:]*:,root:"`/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/host/bin/mkpasswd -m "sha-256" "rockchip"`":, /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/target/etc/shadow
2025-08-30T08:39:22 grep -qsE '^/bin/sh$' /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/target/etc/shells || echo "/bin/sh" >> /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/target/etc/shells
2025-08-30T08:39:22 ln -sf bash /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/target/bin/sh
2025-08-30T08:39:22 /usr/bin/sed -i -e '/^root:/s,[^/]*$,bash,' /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/target/etc/passwd
2025-08-30T08:39:22 if [ -x /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/target/sbin/swapon -a -x /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/target/sbin/swapoff ]; then /usr/bin/sed -i -e '/^#.*\/sbin\/swap/s/^#\+[[:blank:]]*//' /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/target/etc/inittab; else /usr/bin/sed -i -e '/^[^#].*\/sbin\/swap/s/^/#/' /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/target/etc/inittab; fi
2025-08-30T08:39:22 printf '%s\n' C en_US locale-archive > /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/build/locales.nopurge
2025-08-30T08:39:22 for dir in /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/target/usr/share/locale /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/target/usr/share/X11/locale /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/target/usr/lib/locale; do if [ ! -d $dir ]; then continue; fi; for langdir in $dir/*; do if [ -e "${langdir}" ]; then grep -qx "${langdir##*/}" /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/build/locales.nopurge || rm -rf $langdir; fi done; done
2025-08-30T08:39:22 if [ -d /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/target/usr/share/X11/locale ]; then for lang in C en_US; do if [ -f /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/target/usr/share/X11/locale/$lang/XLC_LOCALE ]; then echo "$lang/XLC_LOCALE: $lang"; fi done > /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/target/usr/share/X11/locale/locale.dir; fi
2025-08-30T08:39:22 rm -rf /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/target/usr/share/aclocal \
2025-08-30T08:39:22 /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/target/usr/lib/cmake /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/target/usr/share/cmake \
2025-08-30T08:39:22 /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/target/usr/lib/rpm /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/target/usr/doc
2025-08-30T08:39:22 find /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/target/usr/{lib,share}/ -name '*.cmake' -print0 | xargs -0 rm -f
2025-08-30T08:39:22 find /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/target/lib/ /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/target/usr/lib/ /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/target/usr/libexec/ \
2025-08-30T08:39:22 \( -name '*.la' -o -name '*.prl' \) -print0 | xargs -0 rm -f
2025-08-30T08:39:22 find: '/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/target/usr/libexec/': No such file or directory
2025-08-30T08:39:22 rm -rf /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/target/usr/include \
2025-08-30T08:39:22 /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/target/usr/lib/pkgconfig /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/target/usr/share/pkgconfig
2025-08-30T08:39:22 find /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/target/lib/ /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/target/usr/lib/ /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/target/usr/libexec/ \
2025-08-30T08:39:22 -name '*.a' -print0 | xargs -0 rm -f
2025-08-30T08:39:22 find: '/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/target/usr/libexec/': No such file or directory
2025-08-30T08:39:22 rm -rf /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/target/usr/share/gdb
2025-08-30T08:39:22 rm -rf /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/target/usr/share/bash-completion
2025-08-30T08:39:22 rm -rf /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/target/etc/bash_completion.d
2025-08-30T08:39:22 rm -rf /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/target/usr/share/zsh
2025-08-30T08:39:22 rm -rf /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/target/usr/man /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/target/usr/share/man
2025-08-30T08:39:22 rm -rf /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/target/usr/info /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/target/usr/share/info
2025-08-30T08:39:22 rm -rf /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/target/usr/doc /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/target/usr/share/doc
2025-08-30T08:39:22 rm -rf /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/target/usr/share/gtk-doc
2025-08-30T08:39:22 rmdir /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/target/usr/share 2>/dev/null || true
2025-08-30T08:39:22 rm -rf /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/target/lib/debug /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/target/usr/lib/debug
2025-08-30T08:39:22 find /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/target   -type f \( -perm /111 -o -name '*.so*' \) -not \( -name 'libpthread*.so*' -o -name 'ld-*.so*' -o -name '*.ko' \) -print0 | xargs -0 /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/host/bin/aarch64-buildroot-linux-gnu-strip --remove-section=.comment --remove-section=.note 2>/dev/null || true
2025-08-30T08:39:22 find /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/target   \( -name 'ld-*.so*' -o -name 'libpthread*.so*' \) -print0 | xargs -0 -r /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/host/bin/aarch64-buildroot-linux-gnu-strip --remove-section=.comment --remove-section=.note --strip-debug 2>/dev/null || true
2025-08-30T08:39:22 test -f /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/target/etc/ld.so.conf && \
2025-08-30T08:39:22 { echo "ERROR: we shouldn't have a /etc/ld.so.conf file"; exit 1; } || true
2025-08-30T08:39:22 test -d /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/target/etc/ld.so.conf.d && \
2025-08-30T08:39:22 { echo "ERROR: we shouldn't have a /etc/ld.so.conf.d directory"; exit 1; } || true
2025-08-30T08:39:22 mkdir -p /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/target/etc
2025-08-30T08:39:22 ( \
2025-08-30T08:39:22 echo "NAME=Buildroot"; \
2025-08-30T08:39:22 echo "VERSION=linux-6.1-stan-rkr6.1"; \
2025-08-30T08:39:22 echo "ID=buildroot"; \
2025-08-30T08:39:22 echo "VERSION_ID=2024.02"; \
2025-08-30T08:39:22 echo "PRETTY_NAME=\"Buildroot 2024.02\"" \
2025-08-30T08:39:22 ) >  /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/target/usr/lib/os-release
2025-08-30T08:39:22 ln -sf ../usr/lib/os-release /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/target/etc
2025-08-30T08:39:22 [7m>>>   Sanitizing RPATH in target tree[27m
2025-08-30T08:39:22 PARALLEL_JOBS=23 \
2025-08-30T08:39:22 PER_PACKAGE_DIR=/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/per-package \
2025-08-30T08:39:22 /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/support/scripts/fix-rpath target
2025-08-30T08:39:22 [7m>>>   Sanity check in overlay board/rockchip/common/base[27m
2025-08-30T08:39:23 [7m>>>   Sanity check in overlay board/rockchip/rv1126b/fs-overlay/[27m
2025-08-30T08:39:23 [7m>>>   Copying overlay board/rockchip/common/base[27m
2025-08-30T08:39:23 [7m>>>   Copying overlay board/rockchip/rv1126b/fs-overlay/[27m
2025-08-30T08:39:23 [7m>>>   Executing post-build script board/rockchip/common/post-build.sh[27m
2025-08-30T08:39:23 >>> Ignored board/rockchip/common/overlays/10-weston
2025-08-30T08:39:23 >>> Ignored board/rockchip/common/overlays/11-weston-chromium
2025-08-30T08:39:23 >>> Ignored board/rockchip/common/overlays/20-wlan0
2025-08-30T08:39:23 >>> Copying board/rockchip/common/overlays/default
2025-08-30T08:39:23 sending incremental file list
2025-08-30T08:39:23 
2025-08-30T08:39:23 sent 46 bytes  received 12 bytes  116.00 bytes/sec
2025-08-30T08:39:23 total size is 0  speedup is 0.00
2025-08-30T08:39:23 Executing post-build.sh...
2025-08-30T08:39:23 [35m
2025-08-30T08:39:23 ############### Rockchip Linux SDK ###############
2025-08-30T08:39:23 [0m
2025-08-30T08:39:23 [35mManifest: rv1126b_linux6.1_release_v1.0.0_20250620.xml[0m
2025-08-30T08:39:23 
2025-08-30T08:39:23 [35mLog colors: [0m[36mmessage [0m[35mnotice [0m[34mwarning [0m[91merror [0m[31mfatal[0m
2025-08-30T08:39:23 
2025-08-30T08:39:23 [35mUsing kernel version(6.1) from environment[0m
2025-08-30T08:39:23 [35mUsing rootfs system(buildroot) from environment[0m
2025-08-30T08:39:23 >>> [35mRunning 01-hostname.sh for /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/target (recovery init=busybox)...[0m
2025-08-30T08:39:23 [36mSetting hostname: rv1126bp-recovery[0m
2025-08-30T08:39:23 >>> [35mRunning 10-os-release.sh for /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/target (recovery init=busybox)...[0m
2025-08-30T08:39:23 [36mAdding information to /etc/os-release...[0m
2025-08-30T08:39:23 >>> [35mRunning 30-fstab.sh for /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/target (recovery init=busybox)...[0m
2025-08-30T08:39:23 [36mFixing up /etc/fstab...[0m
2025-08-30T08:39:23 [36mFixing up rootfs type: ext4[0m
2025-08-30T08:39:23 [36mFixup basic partitions for non-systemd init...[0m
2025-08-30T08:39:23 [36mFixing up basic partition: proc /proc[0m
2025-08-30T08:39:23 [36mFixing up basic partition: devtmpfs /dev[0m
2025-08-30T08:39:23 [36mFixing up basic partition: devpts /dev/pts mode=0620,ptmxmode=0000,gid=5[0m
2025-08-30T08:39:23 [36mFixing up basic partition: tmpfs /dev/shm nosuid,nodev,noexec[0m
2025-08-30T08:39:23 [36mFixing up basic partition: sysfs /sys nosuid,nodev,noexec[0m
2025-08-30T08:39:23 [36mFixing up basic partition: configfs /sys/kernel/config[0m
2025-08-30T08:39:23 [36mFixing up basic partition: debugfs /sys/kernel/debug[0m
2025-08-30T08:39:23 [36mFixing up basic partition: pstore /sys/fs/pstore nosuid,nodev,noexec[0m
2025-08-30T08:39:23 [36mFixing up device partition: /dev/sda1 /mnt/udisk auto[0m
2025-08-30T08:39:23 [36mFixing up device partition: /dev/mmcblk1p1 /mnt/sdcard auto[0m
2025-08-30T08:39:23 [36mFixing up device partition: PARTLABEL=oem /oem ext4 defaults[0m
2025-08-30T08:39:23 [36mFixing up device partition: PARTLABEL=userdata /userdata ext4 defaults[0m
2025-08-30T08:39:23 >>> [35mRunning 40-busybox-reboot.sh for /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/target (recovery init=busybox)...[0m
2025-08-30T08:39:23 >>> [35mRunning 50-locale.sh for /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/target (recovery init=busybox)...[0m
2025-08-30T08:39:23 [36mSetting LANG environment to en_US.UTF-8...[0m
2025-08-30T08:39:23 >>> [35mRunning 90-overlay.sh for /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/target (recovery init=busybox)...[0m
2025-08-30T08:39:23 >>> [35mHandling overlay: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/overlays/common/default)...[0m
2025-08-30T08:39:23 sending incremental file list
2025-08-30T08:39:23 ./
2025-08-30T08:39:23 
2025-08-30T08:39:23 sent 53 bytes  received 19 bytes  144.00 bytes/sec
2025-08-30T08:39:23 total size is 0  speedup is 0.00
2025-08-30T08:39:23 >>> [35mInstalling overlay: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/overlays/common/udev-rules to /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/target...[0m
2025-08-30T08:39:23 sending incremental file list
2025-08-30T08:39:23 lib/
2025-08-30T08:39:23 lib/udev/
2025-08-30T08:39:23 
2025-08-30T08:39:23 sent 226 bytes  received 21 bytes  494.00 bytes/sec
2025-08-30T08:39:23 total size is 1,854  speedup is 7.51
2025-08-30T08:39:23 >>> [35mHandling overlay: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/overlays/recovery/adbd)...[0m
2025-08-30T08:39:23 [35mADBD disabled...[0m
2025-08-30T08:39:23 >>> [35mHandling overlay: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/overlays/recovery/default)...[0m
2025-08-30T08:39:23 sending incremental file list
2025-08-30T08:39:23 
2025-08-30T08:39:23 sent 46 bytes  received 12 bytes  116.00 bytes/sec
2025-08-30T08:39:23 total size is 0  speedup is 0.00
2025-08-30T08:39:23 >>> [35mRunning 91-modules.sh for /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/target (recovery init=busybox)...[0m
2025-08-30T08:39:23 [36mStrip kernel modules...[0m
2025-08-30T08:39:23 >>> [35mRunning 97-ldcache.sh for /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/target (recovery init=busybox)...[0m
2025-08-30T08:39:23 >>> [35mRunning 99-owner.sh for /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/target (recovery init=busybox)...[0m
2025-08-30T08:39:24 >>> [35mRunning build.sh - post-rootfs succeeded.[0m
2025-08-30T08:39:24 [35mFiles changed in post-rootfs stage:[0m
2025-08-30T08:39:24 ./etc/hosts
2025-08-30T08:39:24 ./etc/fstab
2025-08-30T08:39:24 ./etc/os-release
2025-08-30T08:39:24 ./etc/hostname
2025-08-30T08:39:24 ./etc/profile.d/lang.sh
2025-08-30T08:39:24 ./lib
2025-08-30T08:39:24 ./mnt/usb_storage
2025-08-30T08:39:24 ./mnt/external_sd
2025-08-30T08:39:24 ./target
2025-08-30T08:39:24 ./sdcard
2025-08-30T08:39:24 ./udisk
2025-08-30T08:39:24 touch /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/target/usr
2025-08-30T08:39:24 [7m>>>   Generating root filesystems common tables[27m
2025-08-30T08:39:24 rm -rf /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/build/buildroot-fs
2025-08-30T08:39:24 mkdir -p /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/build/buildroot-fs
2025-08-30T08:39:24 printf '   	- - input -1 * - - - Input device group\n	- - kvm -1 * - - - kvm nodes\n	- - sgx -1 * - - - SGX device nodes\n\n' >> /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/build/buildroot-fs/full_users_table.txt
2025-08-30T08:39:24 printf '   	/bin/busybox                     f 4755 0  0 - - - - -\n\n' > /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/build/buildroot-fs/full_devices_table.txt
2025-08-30T08:39:24 cat system/device_table.txt >> /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/build/buildroot-fs/full_devices_table.txt
2025-08-30T08:39:24 [7m>>>   Generating filesystem image rootfs.cpio[27m
2025-08-30T08:39:24 mkdir -p /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/images
2025-08-30T08:39:24 rm -rf /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/build/buildroot-fs/cpio
2025-08-30T08:39:24 mkdir -p /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/build/buildroot-fs/cpio
2025-08-30T08:39:24 rsync -auH --exclude=/THIS_IS_NOT_YOUR_ROOT_FILESYSTEM /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/target/ /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/build/buildroot-fs/cpio/target
2025-08-30T08:39:24 echo '#!/bin/sh' > /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/build/buildroot-fs/cpio/fakeroot
2025-08-30T08:39:24 echo "set -e" >> /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/build/buildroot-fs/cpio/fakeroot
2025-08-30T08:39:24 echo "chown -h -R 0:0 /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/build/buildroot-fs/cpio/target" >> /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/build/buildroot-fs/cpio/fakeroot
2025-08-30T08:39:24 PATH="/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/host/bin:/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/host/sbin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin" /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/support/scripts/mkusers /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/build/buildroot-fs/full_users_table.txt /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/build/buildroot-fs/cpio/target >> /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/build/buildroot-fs/cpio/fakeroot
2025-08-30T08:39:24 echo "/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/host/bin/makedevs -d /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/build/buildroot-fs/full_devices_table.txt /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/build/buildroot-fs/cpio/target" >> /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/build/buildroot-fs/cpio/fakeroot
2025-08-30T08:39:24 printf '   	rm -rf /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/build/buildroot-fs/cpio/target/usr/lib/udev/hwdb.d/ /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/build/buildroot-fs/cpio/target/etc/udev/hwdb.d/\n' >> /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/build/buildroot-fs/cpio/fakeroot
2025-08-30T08:39:24 printf '   	if [ ! -e /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/build/buildroot-fs/cpio/target/init ]; then /usr/bin/install -m 0755 fs/cpio/init /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/build/buildroot-fs/cpio/target/init; fi\n	mkdir -p /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/build/buildroot-fs/cpio/target/dev\n	mknod -m 0622 /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/build/buildroot-fs/cpio/target/dev/console c 5 1\n' >> /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/build/buildroot-fs/cpio/fakeroot
2025-08-30T08:39:24 echo "find /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/build/buildroot-fs/cpio/target/run/ -mindepth 1 -prune -print0 | xargs -0r rm -rf --" >> /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/build/buildroot-fs/cpio/fakeroot
2025-08-30T08:39:24 echo "find /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/build/buildroot-fs/cpio/target/tmp/ -mindepth 1 -prune -print0 | xargs -0r rm -rf --" >> /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/build/buildroot-fs/cpio/fakeroot
2025-08-30T08:39:24 printf '   \n' >> /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/build/buildroot-fs/cpio/fakeroot
2025-08-30T08:39:24 printf '   \n' >> /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/build/buildroot-fs/cpio/fakeroot
2025-08-30T08:39:24 printf '   	cd /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/build/buildroot-fs/cpio/target && find . | LC_ALL=C sort | cpio  --quiet -o -H newc > /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/images/rootfs.cpio\n' >> /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/build/buildroot-fs/cpio/fakeroot
2025-08-30T08:39:24 chmod a+x /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/build/buildroot-fs/cpio/fakeroot
2025-08-30T08:39:24 PATH="/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/host/bin:/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/host/sbin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin" FAKEROOTDONTTRYCHOWN=1 /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/host/bin/fakeroot -- /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/build/buildroot-fs/cpio/fakeroot
2025-08-30T08:39:24 rootdir=/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/build/buildroot-fs/cpio/target
2025-08-30T08:39:24 table='/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/build/buildroot-fs/full_devices_table.txt'
2025-08-30T08:39:24 PATH="/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/host/bin:/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/host/sbin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin" gzip -9 -c -n /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/images/rootfs.cpio > /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/images/rootfs.cpio.gz
2025-08-30T08:39:25 [7m>>>   Generating filesystem image rootfs.ext2[27m
2025-08-30T08:39:25 mkdir -p /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/images
2025-08-30T08:39:25 rm -rf /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/build/buildroot-fs/ext2
2025-08-30T08:39:25 mkdir -p /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/build/buildroot-fs/ext2
2025-08-30T08:39:25 rsync -auH --exclude=/THIS_IS_NOT_YOUR_ROOT_FILESYSTEM /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/target/ /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/build/buildroot-fs/ext2/target
2025-08-30T08:39:25 echo '#!/bin/sh' > /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/build/buildroot-fs/ext2/fakeroot
2025-08-30T08:39:25 echo "set -e" >> /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/build/buildroot-fs/ext2/fakeroot
2025-08-30T08:39:25 echo "chown -h -R 0:0 /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/build/buildroot-fs/ext2/target" >> /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/build/buildroot-fs/ext2/fakeroot
2025-08-30T08:39:25 PATH="/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/host/bin:/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/host/sbin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin" /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/support/scripts/mkusers /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/build/buildroot-fs/full_users_table.txt /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/build/buildroot-fs/ext2/target >> /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/build/buildroot-fs/ext2/fakeroot
2025-08-30T08:39:25 echo "/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/host/bin/makedevs -d /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/build/buildroot-fs/full_devices_table.txt /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/build/buildroot-fs/ext2/target" >> /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/build/buildroot-fs/ext2/fakeroot
2025-08-30T08:39:25 printf '   	rm -rf /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/build/buildroot-fs/ext2/target/usr/lib/udev/hwdb.d/ /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/build/buildroot-fs/ext2/target/etc/udev/hwdb.d/\n' >> /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/build/buildroot-fs/ext2/fakeroot
2025-08-30T08:39:25 echo "find /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/build/buildroot-fs/ext2/target/run/ -mindepth 1 -prune -print0 | xargs -0r rm -rf --" >> /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/build/buildroot-fs/ext2/fakeroot
2025-08-30T08:39:25 echo "find /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/build/buildroot-fs/ext2/target/tmp/ -mindepth 1 -prune -print0 | xargs -0r rm -rf --" >> /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/build/buildroot-fs/ext2/fakeroot
2025-08-30T08:39:25 printf '   \n' >> /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/build/buildroot-fs/ext2/fakeroot
2025-08-30T08:39:25 printf '   \n' >> /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/build/buildroot-fs/ext2/fakeroot
2025-08-30T08:39:25 printf '   	rm -f /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/images/rootfs.ext2\n	FILE_SIZE="$(du --apparent-size -sm /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/build/buildroot-fs/ext2/target | cut -f1)"\n	ALIGN_SIZE="$(($(find /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/build/buildroot-fs/ext2/target | wc -l) * 4096 / 1024 / 1024))"\n	ROOTFS_SIZE="$(( ($FILE_SIZE + $ALIGN_SIZE) * 110 / 100 + 64 ))"\n	/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/host/sbin/mkfs.ext4 -d /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/build/buildroot-fs/ext2/target -r 1 -b 4096 -N 0 -m 5 -L "rootfs" -I 256 -O ^64bit /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/images/rootfs.ext2 "${ROOTFS_SIZE}M"\n	/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/host/sbin/resize2fs -M /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/images/rootfs.ext2\n	/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/host/sbin/e2fsck -fy /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/images/rootfs.ext2\n' >> /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/build/buildroot-fs/ext2/fakeroot
2025-08-30T08:39:25 chmod a+x /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/build/buildroot-fs/ext2/fakeroot
2025-08-30T08:39:25 PATH="/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/host/bin:/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/host/sbin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin" FAKEROOTDONTTRYCHOWN=1 /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/host/bin/fakeroot -- /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/build/buildroot-fs/ext2/fakeroot
2025-08-30T08:39:25 rootdir=/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/build/buildroot-fs/ext2/target
2025-08-30T08:39:25 table='/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/build/buildroot-fs/full_devices_table.txt'
2025-08-30T08:39:25 mke2fs 1.47.0 (5-Feb-2023)
2025-08-30T08:39:25 Creating regular file /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/images/rootfs.ext2
2025-08-30T08:39:25 64-bit filesystem support is not enabled.  The larger fields afforded by this feature enable full-strength checksumming.  Pass -O 64bit to rectify.
2025-08-30T08:39:25 Creating filesystem with 20480 4k blocks and 20480 inodes
2025-08-30T08:39:25 
2025-08-30T08:39:25 Allocating group tables: 0/1   done
2025-08-30T08:39:25 Writing inode tables: 0/1   done
2025-08-30T08:39:25 Creating journal (1024 blocks): done
2025-08-30T08:39:25 Copying files into the device: done
2025-08-30T08:39:26 Writing superblocks and filesystem accounting information: 0/1   done
2025-08-30T08:39:26 
2025-08-30T08:39:26 resize2fs 1.47.0 (5-Feb-2023)
2025-08-30T08:39:26 Resizing the filesystem on /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/images/rootfs.ext2 to 5709 (4k) blocks.
2025-08-30T08:39:26 The filesystem on /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/images/rootfs.ext2 is now 5709 (4k) blocks long.
2025-08-30T08:39:26 
2025-08-30T08:39:26 e2fsck 1.47.0 (5-Feb-2023)
2025-08-30T08:39:26 Pass 1: Checking inodes, blocks, and sizes
2025-08-30T08:39:26 Pass 2: Checking directory structure
2025-08-30T08:39:26 Pass 3: Checking directory connectivity
2025-08-30T08:39:26 Pass 4: Checking reference counts
2025-08-30T08:39:26 Pass 5: Checking group summary information
2025-08-30T08:39:26 rootfs: 635/20480 files (0.2% non-contiguous), 5680/5709 blocks
2025-08-30T08:39:26 ln -sf rootfs.ext2 /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/images/rootfs.ext4
2025-08-30T08:39:26 [7m>>>   Generating filesystem image rootfs.squashfs[27m
2025-08-30T08:39:26 mkdir -p /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/images
2025-08-30T08:39:26 rm -rf /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/build/buildroot-fs/squashfs
2025-08-30T08:39:26 mkdir -p /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/build/buildroot-fs/squashfs
2025-08-30T08:39:26 rsync -auH --exclude=/THIS_IS_NOT_YOUR_ROOT_FILESYSTEM /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/target/ /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/build/buildroot-fs/squashfs/target
2025-08-30T08:39:26 echo '#!/bin/sh' > /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/build/buildroot-fs/squashfs/fakeroot
2025-08-30T08:39:26 echo "set -e" >> /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/build/buildroot-fs/squashfs/fakeroot
2025-08-30T08:39:26 echo "chown -h -R 0:0 /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/build/buildroot-fs/squashfs/target" >> /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/build/buildroot-fs/squashfs/fakeroot
2025-08-30T08:39:26 PATH="/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/host/bin:/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/host/sbin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin" /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/support/scripts/mkusers /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/build/buildroot-fs/full_users_table.txt /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/build/buildroot-fs/squashfs/target >> /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/build/buildroot-fs/squashfs/fakeroot
2025-08-30T08:39:26 echo "/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/host/bin/makedevs -d /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/build/buildroot-fs/full_devices_table.txt /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/build/buildroot-fs/squashfs/target" >> /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/build/buildroot-fs/squashfs/fakeroot
2025-08-30T08:39:26 printf '   	rm -rf /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/build/buildroot-fs/squashfs/target/usr/lib/udev/hwdb.d/ /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/build/buildroot-fs/squashfs/target/etc/udev/hwdb.d/\n' >> /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/build/buildroot-fs/squashfs/fakeroot
2025-08-30T08:39:26 echo "find /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/build/buildroot-fs/squashfs/target/run/ -mindepth 1 -prune -print0 | xargs -0r rm -rf --" >> /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/build/buildroot-fs/squashfs/fakeroot
2025-08-30T08:39:26 echo "find /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/build/buildroot-fs/squashfs/target/tmp/ -mindepth 1 -prune -print0 | xargs -0r rm -rf --" >> /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/build/buildroot-fs/squashfs/fakeroot
2025-08-30T08:39:26 printf '   \n' >> /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/build/buildroot-fs/squashfs/fakeroot
2025-08-30T08:39:26 printf '   \n' >> /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/build/buildroot-fs/squashfs/fakeroot
2025-08-30T08:39:26 printf '   	/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/host/bin/mksquashfs /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/build/buildroot-fs/squashfs/target /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/images/rootfs.squashfs -noappend -processors 23 -b 128K  -comp gzip\n' >> /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/build/buildroot-fs/squashfs/fakeroot
2025-08-30T08:39:26 chmod a+x /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/build/buildroot-fs/squashfs/fakeroot
2025-08-30T08:39:26 PATH="/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/host/bin:/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/host/sbin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin" FAKEROOTDONTTRYCHOWN=1 /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/host/bin/fakeroot -- /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/build/buildroot-fs/squashfs/fakeroot
2025-08-30T08:39:26 rootdir=/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/build/buildroot-fs/squashfs/target
2025-08-30T08:39:26 table='/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/build/buildroot-fs/full_devices_table.txt'
2025-08-30T08:39:26 Parallel mksquashfs: Using 23 processors
2025-08-30T08:39:26 Creating 4.0 filesystem on /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/images/rootfs.squashfs, block size 131072.
2025-08-30T08:39:26 
[===============================================================/] 258/258 100%
2025-08-30T08:39:26 
2025-08-30T08:39:26 Exportable Squashfs 4.0 filesystem, gzip compressed, data block size 131072
2025-08-30T08:39:26 compressed data, compressed metadata, compressed fragments,
2025-08-30T08:39:26 compressed xattrs, compressed ids
2025-08-30T08:39:26 duplicates are removed
2025-08-30T08:39:26 Filesystem size 5463.00 Kbytes (5.33 Mbytes)
2025-08-30T08:39:26 43.45% of uncompressed filesystem size (12573.89 Kbytes)
2025-08-30T08:39:26 Inode table size 5107 bytes (4.99 Kbytes)
2025-08-30T08:39:26 22.90% of uncompressed inode table size (22304 bytes)
2025-08-30T08:39:26 Directory table size 5997 bytes (5.86 Kbytes)
2025-08-30T08:39:26 56.88% of uncompressed directory table size (10543 bytes)
2025-08-30T08:39:26 Number of duplicate files found 2
2025-08-30T08:39:26 Number of inodes 624
2025-08-30T08:39:26 Number of files 193
2025-08-30T08:39:26 Number of fragments 30
2025-08-30T08:39:26 Number of symbolic links 360
2025-08-30T08:39:26 Number of device nodes 0
2025-08-30T08:39:26 Number of fifo nodes 0
2025-08-30T08:39:26 Number of socket nodes 0
2025-08-30T08:39:26 Number of directories 71
2025-08-30T08:39:26 Number of hard-links 0
2025-08-30T08:39:26 Number of ids (unique uids + gids) 2
2025-08-30T08:39:26 Number of uids 2
2025-08-30T08:39:26 root (0)
2025-08-30T08:39:26 www-data (33)
2025-08-30T08:39:26 Number of gids 2
2025-08-30T08:39:26 root (0)
2025-08-30T08:39:26 www-data (33)
2025-08-30T08:39:26 [7m>>>   Generating filesystem image rootfs.tar[27m
2025-08-30T08:39:26 mkdir -p /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/images
2025-08-30T08:39:26 rm -rf /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/build/buildroot-fs/tar
2025-08-30T08:39:26 mkdir -p /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/build/buildroot-fs/tar
2025-08-30T08:39:26 rsync -auH --exclude=/THIS_IS_NOT_YOUR_ROOT_FILESYSTEM /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/target/ /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/build/buildroot-fs/tar/target
2025-08-30T08:39:26 echo '#!/bin/sh' > /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/build/buildroot-fs/tar/fakeroot
2025-08-30T08:39:26 echo "set -e" >> /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/build/buildroot-fs/tar/fakeroot
2025-08-30T08:39:26 echo "chown -h -R 0:0 /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/build/buildroot-fs/tar/target" >> /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/build/buildroot-fs/tar/fakeroot
2025-08-30T08:39:26 PATH="/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/host/bin:/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/host/sbin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin" /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/support/scripts/mkusers /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/build/buildroot-fs/full_users_table.txt /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/build/buildroot-fs/tar/target >> /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/build/buildroot-fs/tar/fakeroot
2025-08-30T08:39:26 echo "/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/host/bin/makedevs -d /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/build/buildroot-fs/full_devices_table.txt /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/build/buildroot-fs/tar/target" >> /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/build/buildroot-fs/tar/fakeroot
2025-08-30T08:39:26 printf '   	rm -rf /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/build/buildroot-fs/tar/target/usr/lib/udev/hwdb.d/ /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/build/buildroot-fs/tar/target/etc/udev/hwdb.d/\n' >> /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/build/buildroot-fs/tar/fakeroot
2025-08-30T08:39:26 echo "find /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/build/buildroot-fs/tar/target/run/ -mindepth 1 -prune -print0 | xargs -0r rm -rf --" >> /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/build/buildroot-fs/tar/fakeroot
2025-08-30T08:39:26 echo "find /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/build/buildroot-fs/tar/target/tmp/ -mindepth 1 -prune -print0 | xargs -0r rm -rf --" >> /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/build/buildroot-fs/tar/fakeroot
2025-08-30T08:39:26 printf '   \n' >> /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/build/buildroot-fs/tar/fakeroot
2025-08-30T08:39:26 printf '   \n' >> /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/build/buildroot-fs/tar/fakeroot
2025-08-30T08:39:26 printf '   	(cd /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/build/buildroot-fs/tar/target; find -print0 | LC_ALL=C sort -z | tar  --pax-option=exthdr.name=%%d/PaxHeaders/%%f,atime:=0,ctime:=0 -cf /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/images/rootfs.tar --null --xattrs-include='\''*'\'' --no-recursion -T - --numeric-owner)\n' >> /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/build/buildroot-fs/tar/fakeroot
2025-08-30T08:39:26 chmod a+x /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/build/buildroot-fs/tar/fakeroot
2025-08-30T08:39:26 PATH="/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/host/bin:/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/host/sbin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin" FAKEROOTDONTTRYCHOWN=1 /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/host/bin/fakeroot -- /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/build/buildroot-fs/tar/fakeroot
2025-08-30T08:39:26 rootdir=/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/build/buildroot-fs/tar/target
2025-08-30T08:39:26 table='/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/build/buildroot-fs/full_devices_table.txt'
2025-08-30T08:39:26 make: Leaving directory '/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot'

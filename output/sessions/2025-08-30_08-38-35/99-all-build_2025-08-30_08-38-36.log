# 2025-08-30 08:38:36
# run func: build_hook all

[36m==========================================[0m
[36m          Start building all images[0m
[36m==========================================[0m
[35mGenerated blank misc image[0m
[35mDone packing /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/firmware/misc.img[0m
[35mRunning mk-misc.sh - build_misc succeeded.[0m
[36mToolchain for loader (U-Boot):[0m
[36m/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/prebuilts/gcc/linux-x86/aarch64/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-[0m

[36m==========================================[0m
[36m          Start building U-Boot[0m
[36m==========================================[0m
[35m+ cd u-boot[0m
[35m+ ./make.sh CROSS_COMPILE=/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/prebuilts/gcc/linux-x86/aarch64/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu- rv1126b ../rkbin/RKBOOT/RV1126BPMINIALL.ini --spl-new[0m
## make rv1126b_defconfig -j44
#
# configuration written to .config
#
scripts/kconfig/conf  --silentoldconfig Kconfig
  CHK     include/config.h
  CFG     u-boot.cfg
  GEN     include/autoconf.mk.dep
  CFG     spl/u-boot.cfg
  CFG     tpl/u-boot.cfg
  GEN     include/autoconf.mk
  GEN     tpl/include/autoconf.mk
  GEN     spl/include/autoconf.mk
  CHK     include/config/uboot.release
  CHK     include/generated/timestamp_autogenerated.h
  UPD     include/generated/timestamp_autogenerated.h
  CHK     include/generated/version_autogenerated.h
  CHK     include/generated/generic-asm-offsets.h
  CHK     include/generated/asm-offsets.h
  CHK     include/config.h
  CFG     u-boot.cfg
  HOSTCC  tools/mkenvimage.o
  HOSTCC  tools/fit_image.o
  HOSTCC  tools/image-host.o
  HOSTCC  tools/dumpimage.o
  HOSTCC  tools/mkimage.o
  HOSTLD  tools/mkenvimage
  HOSTLD  tools/dumpimage
  HOSTLD  tools/mkimage
  CC      arch/arm/cpu/armv8/fwcall.o
  CC      arch/arm/mach-rockchip/board.o
  LD      arch/arm/cpu/armv8/built-in.o
  CC      drivers/usb/gadget/f_fastboot.o
  CC      common/main.o
  CC      cmd/version.o
  LD      common/built-in.o
  LD      cmd/built-in.o
  CC      lib/display_options.o
  LD      arch/arm/mach-rockchip/built-in.o
  LD      lib/built-in.o
  LD      drivers/usb/gadget/built-in.o
  LD      u-boot
  OBJCOPY u-boot.srec
  OBJCOPY u-boot-nodtb.bin
  SYM     u-boot.sym
start=$(/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/prebuilts/gcc/linux-x86/aarch64/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-nm u-boot | grep __rel_dyn_start | cut -f 1 -d ' '); end=$(/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/prebuilts/gcc/linux-x86/aarch64/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-nm u-boot | grep __rel_dyn_end | cut -f 1 -d ' '); tools/relocate-rela u-boot-nodtb.bin 0x40200000 $start $end
make[2]: “arch/arm/dts/rv1126b-evb.dtb”已是最新。
  COPY    u-boot.dtb
  CAT     u-boot-dtb.bin
  MKIMAGE u-boot.img
  MKIMAGE u-boot-dtb.img
  COPY    u-boot.bin
  ALIGN   u-boot.bin
  COPY    spl/u-boot-spl.dtb
  CC      spl/common/spl/spl.o
  CC      tpl/arch/arm/mach-rockchip/tpl.o
  CC      spl/arch/arm/cpu/armv8/fwcall.o
  CC      spl/arch/arm/mach-rockchip/spl.o
  CC      tpl/arch/arm/cpu/armv8/fwcall.o
  CC      spl/lib/display_options.o
  LD      tpl/arch/arm/cpu/armv8/built-in.o
  LD      spl/arch/arm/cpu/armv8/built-in.o
  LD      tpl/arch/arm/mach-rockchip/built-in.o
  LD      spl/common/spl/built-in.o
  LD      spl/arch/arm/mach-rockchip/built-in.o
  LD      spl/lib/built-in.o
  LD      tpl/u-boot-tpl
  OBJCOPY tpl/u-boot-tpl-nodtb.bin
  COPY    tpl/u-boot-tpl.bin
  LD      spl/u-boot-spl
  OBJCOPY spl/u-boot-spl-nodtb.bin
  CAT     spl/u-boot-spl-dtb.bin
  COPY    spl/u-boot-spl.bin
  CFGCHK  u-boot.cfg
pack u-boot.itb okay! Input: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/rkbin/RKTRUST/RV1126BTRUST.ini

FIT description: FIT Image with ATF/OP-TEE/U-Boot/MCU
Created:         Sat Aug 30 08:38:39 2025
 Image 0 (uboot)
  Description:  U-Boot
  Created:      Sat Aug 30 08:38:39 2025
  Type:         Standalone Program
  Compression:  lzma compressed
  Data Size:    350297 Bytes = 342.09 KiB = 0.33 MiB
  Architecture: AArch64
  Load Address: 0x40200000
  Entry Point:  unavailable
  Hash algo:    sha256
  Hash value:   ee5dd5ff6afe34a017c64929a2b741fc3b529e847ead9ed9efea446f48b4b86a
 Image 1 (atf-1)
  Description:  ARM Trusted Firmware
  Created:      Sat Aug 30 08:38:39 2025
  Type:         Firmware
  Compression:  lzma compressed
  Data Size:    33662 Bytes = 32.87 KiB = 0.03 MiB
  Architecture: AArch64
  Load Address: 0x40000000
  Hash algo:    sha256
  Hash value:   42352af0db7a55a3a46d92d23bf9db03a74ce91bcd91070b0b12449fd326585c
 Image 2 (atf-2)
  Description:  ARM Trusted Firmware
  Created:      Sat Aug 30 08:38:39 2025
  Type:         Firmware
  Compression:  uncompressed
  Data Size:    8192 Bytes = 8.00 KiB = 0.01 MiB
  Architecture: AArch64
  Load Address: 0x3ffbb000
  Hash algo:    sha256
  Hash value:   8736494db579a3e6fcce3be9666f40885e99cc0775c66042736b2b1d70564942
 Image 3 (atf-3)
  Description:  ARM Trusted Firmware
  Created:      Sat Aug 30 08:38:39 2025
  Type:         Firmware
  Compression:  uncompressed
  Data Size:    6764 Bytes = 6.61 KiB = 0.01 MiB
  Architecture: AArch64
  Load Address: 0x3ff1e000
  Hash algo:    sha256
  Hash value:   c3d9e784a50bcf5e860355fe03eb9293f81ffcb010e89ec4ea7f6a70ea793fb3
 Image 4 (atf-4)
  Description:  ARM Trusted Firmware
  Created:      Sat Aug 30 08:38:39 2025
  Type:         Firmware
  Compression:  uncompressed
  Data Size:    4096 Bytes = 4.00 KiB = 0.00 MiB
  Architecture: AArch64
  Load Address: 0x3ffbd000
  Hash algo:    sha256
  Hash value:   8cb1ae2802d90197b9d860fbbca838237a7e67b00d220f9ac3970c4af8e1eb7c
 Image 5 (fdt)
  Description:  U-Boot dtb
  Created:      Sat Aug 30 08:38:39 2025
  Type:         Flat Device Tree
  Compression:  uncompressed
  Data Size:    7585 Bytes = 7.41 KiB = 0.01 MiB
  Architecture: AArch64
  Hash algo:    sha256
  Hash value:   bc038548e056a130b3b176015c2fd5fba6301121cc627a5e5a34c93ca0e8705e
 Default Configuration: 'conf'
 Configuration 0 (conf)
  Description:  rv1126b-evb
  Kernel:       unavailable
  Firmware:     atf-1
  FDT:          fdt
  Loadables:    uboot
                atf-2
                atf-3
                atf-4
********boot_merger ver 1.35********
Info:Pack loader ok.
pack loader(SPL) okay! Input: ../rkbin/RKBOOT/RV1126BPMINIALL.ini

/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/u-boot
pack loader with new: spl/u-boot-spl.bin

Image(no-signed, version=0): uboot.img (FIT with uboot, trust...) is ready
Image(no-signed): rv1126bp_spl_loader_v1.03.103.bin (with spl, ddr...) is ready
pack uboot.img okay! Input: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/rkbin/RKTRUST/RV1126BTRUST.ini

Platform RV1126B is build OK, with new .config(make rv1126b_defconfig -j44)
/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/prebuilts/gcc/linux-x86/aarch64/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-
Sat Aug 30 08:38:40 CST 2025
[35m+ cd ..[0m
[35mRunning mk-loader.sh - build_uboot succeeded.[0m
[35mUsing kernel version(6.1) from environment[0m
[36mToolchain for kernel:[0m
[36m/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/prebuilts/gcc/linux-x86/aarch64/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-[0m

[36m==========================================[0m
[36m          Start building kernel[0m
[36m==========================================[0m
[36m# Found kernel's basic config fragment: rv1126b.config[0m
[35m+ make -C /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/kernel/ -j23 CROSS_COMPILE=/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/prebuilts/gcc/linux-x86/aarch64/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu- ARCH=arm64 rv1126b_defconfig rv1126b.config[0m
make: Entering directory '/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/kernel-6.1'
#
# configuration written to .config
#
Using .config as base
Merging ./arch/arm64/configs/rv1126b.config
Value of CONFIG_ROCKCHIP_RKNPU_PROC_FS is redefined by fragment ./arch/arm64/configs/rv1126b.config:
Previous value: # CONFIG_ROCKCHIP_RKNPU_PROC_FS is not set
New value: CONFIG_ROCKCHIP_RKNPU_PROC_FS=y

Value of CONFIG_ROCKCHIP_RKNPU_DMA_HEAP is redefined by fragment ./arch/arm64/configs/rv1126b.config:
Previous value: # CONFIG_ROCKCHIP_RKNPU_DMA_HEAP is not set
New value: CONFIG_ROCKCHIP_RKNPU_DMA_HEAP=y

#
# merged configuration written to .config (needs make)
#
.config:5820:warning: override: ROCKCHIP_RKNPU_DMA_HEAP changes choice state
#
# configuration written to .config
#
make: Leaving directory '/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/kernel-6.1'
[35m+ make -C /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/kernel/ -j23 CROSS_COMPILE=/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/prebuilts/gcc/linux-x86/aarch64/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu- ARCH=arm64 rv1126bp-evb-v14.img[0m
make: Entering directory '/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/kernel-6.1'
  SYNC    include/config/auto.conf.cmd
  CALL    scripts/checksyscalls.sh
  Image:  resource.img (with rv1126bp-evb-v14.dtb logo.bmp logo_kernel.bmp) is ready
  Image:  boot.img (with Image  resource.img) is ready
  Image:  zboot.img (with Image.lz4  resource.img) is ready
make: Leaving directory '/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/kernel-6.1'
[35m+ /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/scripts/mk-fitimage.sh kernel/boot.img /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/.chip/boot.its kernel/arch/arm64/boot/Image kernel/arch/arm64/boot/dts/rockchip/rv1126bp-evb-v14.dtb kernel/resource.img[0m
FIT description: U-Boot FIT source file for arm
Created:         Sat Aug 30 08:38:45 2025
 Image 0 (fdt)
  Description:  unavailable
  Created:      Sat Aug 30 08:38:45 2025
  Type:         Flat Device Tree
  Compression:  uncompressed
  Data Size:    68490 Bytes = 66.88 KiB = 0.07 MiB
  Architecture: AArch64
  Load Address: 0xffffff00
  Hash algo:    sha256
  Hash value:   1c05afdd29fb6e150db47a6b11abacaa978ceda0bc748ae9806164800c1c8046
 Image 1 (kernel)
  Description:  unavailable
  Created:      Sat Aug 30 08:38:45 2025
  Type:         Kernel Image
  Compression:  uncompressed
  Data Size:    13174792 Bytes = 12866.01 KiB = 12.56 MiB
  Architecture: AArch64
  OS:           Linux
  Load Address: 0xffffff01
  Entry Point:  0xffffff01
  Hash algo:    sha256
  Hash value:   055cd25c789f3177debc87969b445affdf43e6c4e9c24eb484e01384f8e24d3d
 Image 2 (resource)
  Description:  unavailable
  Created:      Sat Aug 30 08:38:45 2025
  Type:         Multi-File Image
  Compression:  uncompressed
  Data Size:    106496 Bytes = 104.00 KiB = 0.10 MiB
  Hash algo:    sha256
  Hash value:   743c8ff7c5d3c15703691aec25def63213e2903a93b59586b23c7605223e3db3
 Default Configuration: 'conf'
 Configuration 0 (conf)
  Description:  unavailable
  Kernel:       kernel
  FDT:          fdt
[35m+ ln -rsf kernel/boot.img /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/firmware/boot.img[0m
Not Found io-domains in kernel/arch/arm64/boot/dts/rockchip/rv1126bp-evb-v14.dts
[35mRunning mk-kernel.sh - build_kernel succeeded.[0m
[36m==========================================[0m
[36m          Start building rootfs(buildroot)[0m
[36m==========================================[0m
[36m==========================================[0m
[36m          Start building buildroot(2024.02)[0m
[36m==========================================[0m
[35m[0mmake: Entering directory '/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot'
  GEN     /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/Makefile
Parsing defconfig: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/configs/rockchip_rv1126b_ipc_defconfig
Using configs/rockchip/base/kernel.config as base
Merging configs/rockchip/fs/e2fs.config
Merging configs/rockchip/base/common.config
Merging configs/rockchip/base/base.config
Merging configs/rockchip/wifibt/bt.config
Merging configs/rockchip/network/network.config
Merging configs/rockchip/wifibt/wireless.config
Merging configs/rockchip/chips/rv1126b.config
Value of BR2_ROOTFS_OVERLAY is redefined by configs/rockchip/chips/rv1126b.config:
Previous value:	BR2_ROOTFS_OVERLAY="board/rockchip/common/base"
Modify value:	BR2_ROOTFS_OVERLAY+="board/rockchip/rv1126b/fs-overlay/"
New value:	BR2_ROOTFS_OVERLAY="board/rockchip/common/base board/rockchip/rv1126b/fs-overlay/"

Merging configs/rockchip/chips/rv1126b_aarch64.config
Merging configs/rockchip/multimedia/mpp.config
Merging configs/rockchip/multimedia/audio.config
Merging configs/rockchip/multimedia/camera.config
Merging /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/configs/rockchip_rv1126b_ipc_defconfig
Value of BR2_PACKAGE_ALSA_UCM_CONF is redefined by /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/configs/rockchip_rv1126b_ipc_defconfig:
Previous value:	BR2_PACKAGE_ALSA_UCM_CONF=y
Modify value:	# BR2_PACKAGE_ALSA_UCM_CONF is reset to default
New value:	

Value of BR2_PACKAGE_ALSA_UTILS is redefined by /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/configs/rockchip_rv1126b_ipc_defconfig:
Previous value:	BR2_PACKAGE_ALSA_UTILS=y
Modify value:	# BR2_PACKAGE_ALSA_UTILS is reset to default
New value:	

Value of BR2_PACKAGE_LIBMAD is redefined by /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/configs/rockchip_rv1126b_ipc_defconfig:
Previous value:	BR2_PACKAGE_LIBMAD=y
Modify value:	# BR2_PACKAGE_LIBMAD is reset to default
New value:	

Value of BR2_PACKAGE_PULSEAUDIO is redefined by /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/configs/rockchip_rv1126b_ipc_defconfig:
Previous value:	BR2_PACKAGE_PULSEAUDIO=y
Modify value:	# BR2_PACKAGE_PULSEAUDIO is reset to default
New value:	

Value of BR2_ROOTFS_OVERLAY is redefined by /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/configs/rockchip_rv1126b_ipc_defconfig:
Previous value:	BR2_ROOTFS_OVERLAY="board/rockchip/common/base board/rockchip/rv1126b/fs-overlay/"
New value:	BR2_ROOTFS_OVERLAY="board/rockchip/common/base board/rockchip/rv1126b/fs-overlay-ipc/"

#
# merged configuration written to /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/.config.in (needs make)
#
#
# configuration written to /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/.config
#
make: Leaving directory '/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot'
2025-08-30T08:38:52 [7m>>>   Finalizing host directory[27m
2025-08-30T08:38:52 [7m>>>   Finalizing target directory[27m
2025-08-30T08:38:52 [7m>>>   Sanitizing RPATH in target tree[27m
2025-08-30T08:38:54 [7m>>>   Sanity check in overlay board/rockchip/common/base[27m
2025-08-30T08:38:54 [7m>>>   Sanity check in overlay board/rockchip/rv1126b/fs-overlay-ipc/[27m
2025-08-30T08:38:54 [7m>>>   Copying overlay board/rockchip/common/base[27m
2025-08-30T08:38:54 [7m>>>   Copying overlay board/rockchip/rv1126b/fs-overlay-ipc/[27m
2025-08-30T08:38:54 [7m>>>   Executing post-build script board/rockchip/common/post-build.sh[27m
2025-08-30T08:38:54 >>> Ignored board/rockchip/common/overlays/10-weston
2025-08-30T08:38:54 >>> Ignored board/rockchip/common/overlays/11-weston-chromium
2025-08-30T08:38:54 >>> Copying board/rockchip/common/overlays/20-wlan0
2025-08-30T08:38:54 >>> Copying board/rockchip/common/overlays/default
2025-08-30T08:38:55 >>> [35mRunning 00-wifibt.sh for /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target (buildroot init=busybox)...[0m
2025-08-30T08:38:55 >>> [36mBuilding Wifi/BT module and firmwares...[0m
2025-08-30T08:38:55 >>> [35mRunning 01-hostname.sh for /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target (buildroot init=busybox)...[0m
2025-08-30T08:38:55 >>> [35mRunning 10-os-release.sh for /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target (buildroot init=busybox)...[0m
2025-08-30T08:38:55 >>> [35mRunning 20-info.sh for /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target (buildroot init=busybox)...[0m
2025-08-30T08:38:55 >>> [35mRunning 30-fstab.sh for /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target (buildroot init=busybox)...[0m
2025-08-30T08:38:55 >>> [35mRunning 40-busybox-reboot.sh for /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target (buildroot init=busybox)...[0m
2025-08-30T08:38:55 >>> [35mRunning 50-locale.sh for /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target (buildroot init=busybox)...[0m
2025-08-30T08:38:55 >>> [35mRunning 90-overlay.sh for /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target (buildroot init=busybox)...[0m
2025-08-30T08:38:55 >>> [35mHandling overlay: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/overlays/common/default)...[0m
2025-08-30T08:38:55 >>> [35mInstalling overlay: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/overlays/common/udev-rules to /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target...[0m
2025-08-30T08:38:55 >>> [35mHandling overlay: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/overlays/rootfs/async-commit)...[0m
2025-08-30T08:38:55 >>> [35mHandling overlay: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/overlays/rootfs/bootanim)...[0m
2025-08-30T08:38:55 >>> [35mHandling overlay: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/overlays/rootfs/chromium)...[0m
2025-08-30T08:38:55 >>> [35mInstalling overlay: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/overlays/rootfs/default to /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target...[0m
2025-08-30T08:38:55 >>> [35mHandling overlay: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/overlays/rootfs/disk-helpers)...[0m
2025-08-30T08:38:55 >>> [36mInstalling mount service...[0m
2025-08-30T08:38:55 >>> [35mHandling overlay: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/fonts)...[0m
2025-08-30T08:38:55 >>> [35mHandling overlay: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/overlays/rootfs/frecon)...[0m
2025-08-30T08:38:55 >>> [35mHandling overlay: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/overlays/rootfs/fstrim)...[0m
2025-08-30T08:38:55 >>> [35mHandling overlay: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/overlays/rootfs/generate-logs)...[0m
2025-08-30T08:38:55 >>> [35mHandling overlay: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/overlays/rootfs/input-event-daemon)...[0m
2025-08-30T08:38:55 >>> [35mHandling overlay: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/overlays/rootfs/irqbalance)...[0m
2025-08-30T08:38:55 >>> [35mHandling overlay: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/overlays/rootfs/log-guardian)...[0m
2025-08-30T08:38:55 >>> [35mHandling overlay: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/tools)...[0m
2025-08-30T08:38:55 >>> [35mHandling overlay: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/overlays/rootfs/usb-gadget)...[0m
2025-08-30T08:38:55 >>> [36mInstalling USB gadget service...[0m
2025-08-30T08:38:55 >>> [36mInstalling ADBD...[0m
2025-08-30T08:38:55 >>> [35mRunning 91-modules.sh for /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target (buildroot init=busybox)...[0m
2025-08-30T08:38:56 >>> [35mRunning 95-extra-parts.sh for /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target (buildroot init=busybox)...[0m
2025-08-30T08:38:56 >>> [35mRunning 97-ldcache.sh for /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target (buildroot init=busybox)...[0m
2025-08-30T08:38:56 >>> [35mRunning 99-owner.sh for /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target (buildroot init=busybox)...[0m
2025-08-30T08:38:56 >>> [35mRunning build.sh - post-rootfs succeeded.[0m
2025-08-30T08:38:56 [7m>>>   Generating root filesystems common tables[27m
2025-08-30T08:38:56 [7m>>>   Generating filesystem image rootfs.cpio[27m
2025-08-30T08:39:10 [7m>>>   Generating filesystem image rootfs.ext2[27m
2025-08-30T08:39:12 [7m>>>   Generating filesystem image rootfs.squashfs[27m
2025-08-30T08:39:14 [7m>>>   Generating filesystem image rootfs.tar[27m
Done in 29s
[35mLog saved on /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/sessions/2025-08-30_08-38-35/br-rockchip_rv1126b_ipc_2025-08-30_08-38-46.log[0m
[35mGenerated images:[0m
/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/buildroot/images/rootfs.cpio
/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/buildroot/images/rootfs.cpio.gz
/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/buildroot/images/rootfs.ext2
/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/buildroot/images/rootfs.ext4
/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/buildroot/images/rootfs.squashfs
/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/buildroot/images/rootfs.tar
# 2025-08-30 08:38:55
# run hook: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target

[35mRunning 00-wifibt.sh for /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target (buildroot init=busybox)...[0m
[36mBuilding Wifi/BT module and firmwares...[0m
[35mSkipping 00-wifibt.sh - build_wifibt for missing configs:  RK_WIFIBT RK_WIFIBT_MODULES.[0m
[35mRunning 01-hostname.sh for /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target (buildroot init=busybox)...[0m
[36mSetting hostname: rv1126bp-buildroot[0m
[35mRunning 10-os-release.sh for /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target (buildroot init=busybox)...[0m
[36mAdding information to /etc/os-release...[0m
[35mRunning 20-info.sh for /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target (buildroot init=busybox)...[0m
[35mRunning 30-fstab.sh for /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target (buildroot init=busybox)...[0m
[36mFixing up /etc/fstab...[0m
[36mFixing up rootfs type: ext4[0m
[36mFixup basic partitions for non-systemd init...[0m
[36mFixing up basic partition: proc /proc[0m
[36mFixing up basic partition: devtmpfs /dev[0m
[36mFixing up basic partition: devpts /dev/pts mode=0620,ptmxmode=0000,gid=5[0m
[36mFixing up basic partition: tmpfs /dev/shm nosuid,nodev,noexec[0m
[36mFixing up basic partition: sysfs /sys nosuid,nodev,noexec[0m
[36mFixing up basic partition: configfs /sys/kernel/config[0m
[36mFixing up basic partition: debugfs /sys/kernel/debug[0m
[36mFixing up basic partition: pstore /sys/fs/pstore nosuid,nodev,noexec[0m
[36mFixing up device partition: PARTLABEL=oem /oem ext4 defaults[0m
[36mFixing up device partition: PARTLABEL=userdata /userdata ext4 defaults[0m
[35mRunning 40-busybox-reboot.sh for /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target (buildroot init=busybox)...[0m
[35mRunning 50-locale.sh for /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target (buildroot init=busybox)...[0m
[36mSetting LANG environment to en_US.UTF-8...[0m
[35mRunning 90-overlay.sh for /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target (buildroot init=busybox)...[0m
[35mHandling overlay: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/overlays/common/default)...[0m
sending incremental file list
./

sent 53 bytes  received 19 bytes  144.00 bytes/sec
total size is 0  speedup is 0.00
[35mInstalling overlay: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/overlays/common/udev-rules to /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target...[0m
sending incremental file list
lib/
lib/udev/
lib/udev/rules.d/

sent 229 bytes  received 24 bytes  506.00 bytes/sec
total size is 1,854  speedup is 7.33
[35mHandling overlay: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/overlays/rootfs/async-commit)...[0m
[35mHandling overlay: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/overlays/rootfs/bootanim)...[0m
[35mHandling overlay: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/overlays/rootfs/chromium)...[0m
[35mInstalling overlay: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/overlays/rootfs/default to /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target...[0m
sending incremental file list
./

sent 53 bytes  received 19 bytes  144.00 bytes/sec
total size is 0  speedup is 0.00
[35mHandling overlay: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/overlays/rootfs/disk-helpers)...[0m
[36mInstalling mount service...[0m
sending incremental file list
usr/
usr/bin/

sent 158 bytes  received 24 bytes  364.00 bytes/sec
total size is 13,953  speedup is 76.66
[35mHandling overlay: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/fonts)...[0m
[35mNo extra fonts for buildroot by default[0m
[35mHandling overlay: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/overlays/rootfs/frecon)...[0m
[35mHandling overlay: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/overlays/rootfs/fstrim)...[0m
[35mHandling overlay: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/overlays/rootfs/generate-logs)...[0m
[35mHandling overlay: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/overlays/rootfs/input-event-daemon)...[0m
[35mDisabling input-event-daemon...[0m
[35mHandling overlay: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/overlays/rootfs/irqbalance)...[0m
[35mHandling overlay: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/overlays/rootfs/log-guardian)...[0m
[35mHandling overlay: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/tools)...[0m
[35mHandling overlay: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/overlays/rootfs/usb-gadget)...[0m
[36mInstalling USB gadget service...[0m
[36mUSB gadget functions: adb[0m
[36mInstalling ADBD...[0m
sending incremental file list
lib/
lib/udev/
lib/udev/rules.d/
usr/bin/
usr/bin/usbdevice

sent 17,391 bytes  received 52 bytes  34,886.00 bytes/sec
total size is 17,268  speedup is 0.99
[35mRunning 91-modules.sh for /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target (buildroot init=busybox)...[0m
[36mStrip kernel modules...[0m
copy from `/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target/usr/lib/module/rockit.ko' [elf64-littleaarch64] to `/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target/usr/lib/module/stutGwi0' [elf64-littleaarch64]
copy from `/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target/usr/lib/module/kmpp.ko' [elf64-littleaarch64] to `/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target/usr/lib/module/st23n8Nu' [elf64-littleaarch64]
[35mRunning 95-extra-parts.sh for /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target (buildroot init=busybox)...[0m
[36mPreparing extra partitions...[0m
[35mRunning 97-ldcache.sh for /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target (buildroot init=busybox)...[0m
[35mRunning 99-owner.sh for /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target (buildroot init=busybox)...[0m
[35mRunning mk-rootfs.sh - build_buildroot /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/buildroot/images succeeded.[0m
[35mRunning mk-rootfs.sh - build_rootfs succeeded.[0m
[36m==========================================[0m
[36m          Start building recovery(buildroot)[0m
[36m==========================================[0m
[35m[0mmake: Entering directory '/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot'
  GEN     /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/Makefile
Parsing defconfig: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/configs/rockchip_rv1126b_recovery_defconfig
Using configs/rockchip/base/kernel.config as base
Merging configs/rockchip/fs/e2fs.config
Merging configs/rockchip/base/common.config
Merging configs/rockchip/base/base.config
Merging configs/rockchip/base/kernel.config
Merging configs/rockchip/fs/e2fs.config
Merging configs/rockchip/base/common.config
Merging configs/rockchip/fs/vfat.config
Merging configs/rockchip/base/recovery.config
Merging configs/rockchip/chips/rv1126b.config
Value of BR2_ROOTFS_OVERLAY is redefined by configs/rockchip/chips/rv1126b.config:
Previous value:	BR2_ROOTFS_OVERLAY="board/rockchip/common/base"
Modify value:	BR2_ROOTFS_OVERLAY+="board/rockchip/rv1126b/fs-overlay/"
New value:	BR2_ROOTFS_OVERLAY="board/rockchip/common/base board/rockchip/rv1126b/fs-overlay/"

Merging configs/rockchip/chips/rv1126b_aarch64.config
Merging /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/configs/rockchip_rv1126b_recovery_defconfig
#
# merged configuration written to /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/.config.in (needs make)
#
#
# configuration written to /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/.config
#
make: Leaving directory '/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot'
2025-08-30T08:39:22 [7m>>>   Finalizing host directory[27m
2025-08-30T08:39:22 [7m>>>   Finalizing target directory[27m
2025-08-30T08:39:22 [7m>>>   Sanitizing RPATH in target tree[27m
2025-08-30T08:39:22 [7m>>>   Sanity check in overlay board/rockchip/common/base[27m
2025-08-30T08:39:23 [7m>>>   Sanity check in overlay board/rockchip/rv1126b/fs-overlay/[27m
2025-08-30T08:39:23 [7m>>>   Copying overlay board/rockchip/common/base[27m
2025-08-30T08:39:23 [7m>>>   Copying overlay board/rockchip/rv1126b/fs-overlay/[27m
2025-08-30T08:39:23 [7m>>>   Executing post-build script board/rockchip/common/post-build.sh[27m
2025-08-30T08:39:23 >>> Ignored board/rockchip/common/overlays/10-weston
2025-08-30T08:39:23 >>> Ignored board/rockchip/common/overlays/11-weston-chromium
2025-08-30T08:39:23 >>> Ignored board/rockchip/common/overlays/20-wlan0
2025-08-30T08:39:23 >>> Copying board/rockchip/common/overlays/default
2025-08-30T08:39:23 >>> [35mRunning 01-hostname.sh for /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/target (recovery init=busybox)...[0m
2025-08-30T08:39:23 >>> [35mRunning 10-os-release.sh for /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/target (recovery init=busybox)...[0m
2025-08-30T08:39:23 >>> [35mRunning 30-fstab.sh for /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/target (recovery init=busybox)...[0m
2025-08-30T08:39:23 >>> [35mRunning 40-busybox-reboot.sh for /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/target (recovery init=busybox)...[0m
2025-08-30T08:39:23 >>> [35mRunning 50-locale.sh for /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/target (recovery init=busybox)...[0m
2025-08-30T08:39:23 >>> [35mRunning 90-overlay.sh for /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/target (recovery init=busybox)...[0m
2025-08-30T08:39:23 >>> [35mHandling overlay: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/overlays/common/default)...[0m
2025-08-30T08:39:23 >>> [35mInstalling overlay: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/overlays/common/udev-rules to /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/target...[0m
2025-08-30T08:39:23 >>> [35mHandling overlay: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/overlays/recovery/adbd)...[0m
2025-08-30T08:39:23 >>> [35mHandling overlay: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/overlays/recovery/default)...[0m
2025-08-30T08:39:23 >>> [35mRunning 91-modules.sh for /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/target (recovery init=busybox)...[0m
2025-08-30T08:39:23 >>> [35mRunning 97-ldcache.sh for /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/target (recovery init=busybox)...[0m
2025-08-30T08:39:23 >>> [35mRunning 99-owner.sh for /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/target (recovery init=busybox)...[0m
2025-08-30T08:39:24 >>> [35mRunning build.sh - post-rootfs succeeded.[0m
2025-08-30T08:39:24 [7m>>>   Generating root filesystems common tables[27m
2025-08-30T08:39:24 [7m>>>   Generating filesystem image rootfs.cpio[27m
2025-08-30T08:39:25 [7m>>>   Generating filesystem image rootfs.ext2[27m
2025-08-30T08:39:26 [7m>>>   Generating filesystem image rootfs.squashfs[27m
2025-08-30T08:39:26 [7m>>>   Generating filesystem image rootfs.tar[27m
Done in 9s
[35mLog saved on /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/sessions/2025-08-30_08-38-35/br-rockchip_rv1126b_recovery_2025-08-30_08-39-17.log[0m
[35mGenerated images:[0m
/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/recovery/images/rootfs.cpio
/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/recovery/images/rootfs.cpio.gz
/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/recovery/images/rootfs.ext2
/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/recovery/images/rootfs.ext4
/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/recovery/images/rootfs.squashfs
/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/recovery/images/rootfs.tar
[35mUsing kernel version(6.1) from environment[0m
[36mToolchain for kernel:[0m
[36m/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/prebuilts/gcc/linux-x86/aarch64/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-[0m

[36m==========================================[0m
[36m          Start building recovery-kernel[0m
[36m==========================================[0m
[35m+ rm -rf /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/recovery-kernel[0m
[35m+ ln -rsf /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/kernel /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/recovery-kernel[0m
[35m+ cd /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/recovery-kernel[0m
[36m# Found kernel's basic config fragment: rv1126b.config[0m
[35m+ make -C /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/kernel/ -j23 CROSS_COMPILE=/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/prebuilts/gcc/linux-x86/aarch64/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu- ARCH=arm64 rv1126b_defconfig rv1126b.config[0m
make: Entering directory '/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/kernel-6.1'
#
# configuration written to .config
#
Using .config as base
Merging ./arch/arm64/configs/rv1126b.config
Value of CONFIG_ROCKCHIP_RKNPU_PROC_FS is redefined by fragment ./arch/arm64/configs/rv1126b.config:
Previous value: # CONFIG_ROCKCHIP_RKNPU_PROC_FS is not set
New value: CONFIG_ROCKCHIP_RKNPU_PROC_FS=y

Value of CONFIG_ROCKCHIP_RKNPU_DMA_HEAP is redefined by fragment ./arch/arm64/configs/rv1126b.config:
Previous value: # CONFIG_ROCKCHIP_RKNPU_DMA_HEAP is not set
New value: CONFIG_ROCKCHIP_RKNPU_DMA_HEAP=y

#
# merged configuration written to .config (needs make)
#
.config:5820:warning: override: ROCKCHIP_RKNPU_DMA_HEAP changes choice state
#
# configuration written to .config
#
make: Leaving directory '/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/kernel-6.1'
[35m+ make -C /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/kernel/ -j23 CROSS_COMPILE=/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/prebuilts/gcc/linux-x86/aarch64/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu- ARCH=arm64 Image[0m
make: Entering directory '/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/kernel-6.1'
  SYNC    include/config/auto.conf.cmd
  CALL    scripts/checksyscalls.sh
make: Leaving directory '/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/kernel-6.1'
[35m+ make -C /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/kernel/ -j23 CROSS_COMPILE=/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/prebuilts/gcc/linux-x86/aarch64/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu- ARCH=arm64 rockchip/rv1126bp-evb-v14.dtb[0m
make: Entering directory '/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/kernel-6.1'
make: Leaving directory '/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/kernel-6.1'
[35m+ ln -rsf arch/arm64/boot/Image /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/recovery-kernel.img[0m
[35m+ ln -rsf arch/arm64/boot/dts/rockchip/rv1126bp-evb-v14.dtb /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/recovery-kernel.dtb[0m
[35m+ scripts/resource_tool arch/arm64/boot/dts/rockchip/rv1126bp-evb-v14.dtb logo.bmp logo_kernel.bmp[0m
Pack to resource.img successed!
[35m+ ln -rsf resource.img /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/recovery-resource.img[0m
[35mRunning mk-kernel.sh - build_recovery-kernel succeeded.[0m
Packing /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/recovery/ramboot.img...
FIT description: U-Boot FIT source file for arm
Created:         Sat Aug 30 08:39:31 2025
 Image 0 (fdt)
  Description:  unavailable
  Created:      Sat Aug 30 08:39:31 2025
  Type:         Flat Device Tree
  Compression:  uncompressed
  Data Size:    68490 Bytes = 66.88 KiB = 0.07 MiB
  Architecture: AArch64
  Load Address: 0xffffff00
  Hash algo:    sha256
  Hash value:   1c05afdd29fb6e150db47a6b11abacaa978ceda0bc748ae9806164800c1c8046
 Image 1 (kernel)
  Description:  unavailable
  Created:      Sat Aug 30 08:39:31 2025
  Type:         Kernel Image
  Compression:  uncompressed
  Data Size:    13174792 Bytes = 12866.01 KiB = 12.56 MiB
  Architecture: AArch64
  OS:           Linux
  Load Address: 0xffffff01
  Entry Point:  0xffffff01
  Hash algo:    sha256
  Hash value:   055cd25c789f3177debc87969b445affdf43e6c4e9c24eb484e01384f8e24d3d
 Image 2 (ramdisk)
  Description:  unavailable
  Created:      Sat Aug 30 08:39:31 2025
  Type:         RAMDisk Image
  Compression:  uncompressed
  Data Size:    5646422 Bytes = 5514.08 KiB = 5.38 MiB
  Architecture: AArch64
  OS:           Linux
  Load Address: 0xffffff02
  Entry Point:  unavailable
  Hash algo:    sha256
  Hash value:   3d92b7f01d9bba9ec6e138ea62e9963c177590c6a5ff9eb23b441854b1eac971
 Image 3 (resource)
  Description:  unavailable
  Created:      Sat Aug 30 08:39:31 2025
  Type:         Multi-File Image
  Compression:  uncompressed
  Data Size:    106496 Bytes = 104.00 KiB = 0.10 MiB
  Hash algo:    sha256
  Hash value:   30d8be602c2723199411a361c4689606b2077c737053dad3f84fb8d8a696e9d4
 Default Configuration: 'conf'
 Configuration 0 (conf)
  Description:  unavailable
  Kernel:       kernel
  Init Ramdisk: ramdisk
  FDT:          fdt
[35mRunning mk-recovery.sh - build_recovery succeeded.[0m
[36m==========================================[0m
[36m          Start packing firmwares[0m
[36m==========================================[0m
[36mLinking /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/firmware/parameter.txt from /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/.chip/parameter.txt...[0m
[35mPreparing partiton oem[0m
[36mMerging /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/extra-parts/oem/normal into /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/extra-parts/oem[0m
[35mPacking /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/extra-parts/oem.img from /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/extra-parts/oem.fs[0m
Using host tools in /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/latest/host (except for mke2fs)
Making /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/extra-parts/oem.img from /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/extra-parts/oem (auto sized)
Making /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/extra-parts/oem.img from /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/extra-parts/oem with size(13656KB)
/sbin/mke2fs: symbol lookup error: /sbin/mke2fs: undefined symbol: test_io_manager
Retring with increased size....(1/20)
Making /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/extra-parts/oem.img from /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/extra-parts/oem with size(17752KB)
/sbin/mke2fs: symbol lookup error: /sbin/mke2fs: undefined symbol: test_io_manager
Retring with increased size....(2/20)
Making /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/extra-parts/oem.img from /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/extra-parts/oem with size(21848KB)
/sbin/mke2fs: symbol lookup error: /sbin/mke2fs: undefined symbol: test_io_manager
Retring with increased size....(3/20)
Making /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/extra-parts/oem.img from /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/extra-parts/oem with size(25944KB)
/sbin/mke2fs: symbol lookup error: /sbin/mke2fs: undefined symbol: test_io_manager
Retring with increased size....(4/20)
Making /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/extra-parts/oem.img from /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/extra-parts/oem with size(30040KB)
/sbin/mke2fs: symbol lookup error: /sbin/mke2fs: undefined symbol: test_io_manager
Retring with increased size....(5/20)
Making /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/extra-parts/oem.img from /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/extra-parts/oem with size(34136KB)
/sbin/mke2fs: symbol lookup error: /sbin/mke2fs: undefined symbol: test_io_manager
Retring with increased size....(6/20)
Making /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/extra-parts/oem.img from /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/extra-parts/oem with size(38232KB)
/sbin/mke2fs: symbol lookup error: /sbin/mke2fs: undefined symbol: test_io_manager
Retring with increased size....(7/20)
Making /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/extra-parts/oem.img from /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/extra-parts/oem with size(42328KB)
/sbin/mke2fs: symbol lookup error: /sbin/mke2fs: undefined symbol: test_io_manager
Retring with increased size....(8/20)
Making /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/extra-parts/oem.img from /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/extra-parts/oem with size(46424KB)
/sbin/mke2fs: symbol lookup error: /sbin/mke2fs: undefined symbol: test_io_manager
Retring with increased size....(9/20)
Making /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/extra-parts/oem.img from /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/extra-parts/oem with size(50520KB)
/sbin/mke2fs: symbol lookup error: /sbin/mke2fs: undefined symbol: test_io_manager
Retring with increased size....(10/20)
Making /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/extra-parts/oem.img from /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/extra-parts/oem with size(54616KB)
/sbin/mke2fs: symbol lookup error: /sbin/mke2fs: undefined symbol: test_io_manager
Retring with increased size....(11/20)
Making /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/extra-parts/oem.img from /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/extra-parts/oem with size(58712KB)
/sbin/mke2fs: symbol lookup error: /sbin/mke2fs: undefined symbol: test_io_manager
Retring with increased size....(12/20)
Making /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/extra-parts/oem.img from /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/extra-parts/oem with size(62808KB)
/sbin/mke2fs: symbol lookup error: /sbin/mke2fs: undefined symbol: test_io_manager
Retring with increased size....(13/20)
Making /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/extra-parts/oem.img from /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/extra-parts/oem with size(66904KB)
/sbin/mke2fs: symbol lookup error: /sbin/mke2fs: undefined symbol: test_io_manager
Retring with increased size....(14/20)
Making /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/extra-parts/oem.img from /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/extra-parts/oem with size(71000KB)
/sbin/mke2fs: symbol lookup error: /sbin/mke2fs: undefined symbol: test_io_manager
Retring with increased size....(15/20)
Making /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/extra-parts/oem.img from /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/extra-parts/oem with size(75096KB)
/sbin/mke2fs: symbol lookup error: /sbin/mke2fs: undefined symbol: test_io_manager
Retring with increased size....(16/20)
Making /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/extra-parts/oem.img from /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/extra-parts/oem with size(79192KB)
/sbin/mke2fs: symbol lookup error: /sbin/mke2fs: undefined symbol: test_io_manager
Retring with increased size....(17/20)
Making /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/extra-parts/oem.img from /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/extra-parts/oem with size(83288KB)
/sbin/mke2fs: symbol lookup error: /sbin/mke2fs: undefined symbol: test_io_manager
Retring with increased size....(18/20)
Making /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/extra-parts/oem.img from /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/extra-parts/oem with size(87384KB)
/sbin/mke2fs: symbol lookup error: /sbin/mke2fs: undefined symbol: test_io_manager
Retring with increased size....(19/20)
Making /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/extra-parts/oem.img from /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/extra-parts/oem with size(91480KB)
/sbin/mke2fs: symbol lookup error: /sbin/mke2fs: undefined symbol: test_io_manager
Retring with increased size....(20/20)
Making /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/extra-parts/oem.img from /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/extra-parts/oem with size(95576KB)
/sbin/mke2fs: symbol lookup error: /sbin/mke2fs: undefined symbol: test_io_manager
FATAL:  Failed to make image!
[31mERROR: Running /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/scripts/mk-extra-parts.sh - post_build_hook failed![0m
[31mERROR: exit code 1 from line 74:[0m
[31m    fakeroot -- "$FAKEROOT_SCRIPT"[0m
[31mERROR: call stack:[0m
[31m    mk-extra-parts.sh: post_build_hook(74)[0m
[31m    mk-extra-parts.sh: main(89)[0m
[31mERROR: Running /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/scripts/mk-firmware.sh - build_firmware failed![0m
[31mERROR: exit code 1 from line 22:[0m
[31m    "$RK_SCRIPTS_DIR/mk-extra-parts.sh"[0m
[31mERROR: call stack:[0m
[31m    mk-firmware.sh: build_firmware(22)[0m
[31m    mk-firmware.sh: post_build_hook(93)[0m
[31m    mk-firmware.sh: main(98)[0m
[31mERROR: Running /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/build-hooks/99-all.sh - build_all failed![0m
[31mERROR: exit code 1 from line 28:[0m
[31m    "$RK_SCRIPTS_DIR/mk-firmware.sh"[0m
[31mERROR: call stack:[0m
[31m    99-all.sh: build_all(28)[0m
[31m    99-all.sh: build_hook(136)[0m
[31m    build-helper: try_func(63)[0m
[31m    build-helper: try_hook(96)[0m
[31m    build-helper: source(165)[0m
[31m    99-all.sh: main(147)[0m

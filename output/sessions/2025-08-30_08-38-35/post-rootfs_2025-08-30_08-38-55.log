# 2025-08-30 08:38:55
# run hook: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target

[35mRunning 00-wifibt.sh for /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target (buildroot init=busybox)...[0m
[36mBuilding Wifi/BT module and firmwares...[0m
[35mSkipping 00-wifibt.sh - build_wifibt for missing configs:  RK_WIFIBT RK_WIFIBT_MODULES.[0m
[35mRunning 01-hostname.sh for /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target (buildroot init=busybox)...[0m
[36mSetting hostname: rv1126bp-buildroot[0m
[35mRunning 10-os-release.sh for /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target (buildroot init=busybox)...[0m
[36mAdding information to /etc/os-release...[0m
[35mRunning 20-info.sh for /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target (buildroot init=busybox)...[0m
[35mRunning 30-fstab.sh for /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target (buildroot init=busybox)...[0m
[36mFixing up /etc/fstab...[0m
[36mFixing up rootfs type: ext4[0m
[36mFixup basic partitions for non-systemd init...[0m
[36mFixing up basic partition: proc /proc[0m
[36mFixing up basic partition: devtmpfs /dev[0m
[36mFixing up basic partition: devpts /dev/pts mode=0620,ptmxmode=0000,gid=5[0m
[36mFixing up basic partition: tmpfs /dev/shm nosuid,nodev,noexec[0m
[36mFixing up basic partition: sysfs /sys nosuid,nodev,noexec[0m
[36mFixing up basic partition: configfs /sys/kernel/config[0m
[36mFixing up basic partition: debugfs /sys/kernel/debug[0m
[36mFixing up basic partition: pstore /sys/fs/pstore nosuid,nodev,noexec[0m
[36mFixing up device partition: PARTLABEL=oem /oem ext4 defaults[0m
[36mFixing up device partition: PARTLABEL=userdata /userdata ext4 defaults[0m
[35mRunning 40-busybox-reboot.sh for /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target (buildroot init=busybox)...[0m
[35mRunning 50-locale.sh for /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target (buildroot init=busybox)...[0m
[36mSetting LANG environment to en_US.UTF-8...[0m
[35mRunning 90-overlay.sh for /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target (buildroot init=busybox)...[0m
[35mHandling overlay: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/overlays/common/default)...[0m
sending incremental file list
./

sent 53 bytes  received 19 bytes  144.00 bytes/sec
total size is 0  speedup is 0.00
[35mInstalling overlay: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/overlays/common/udev-rules to /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target...[0m
sending incremental file list
lib/
lib/udev/
lib/udev/rules.d/

sent 229 bytes  received 24 bytes  506.00 bytes/sec
total size is 1,854  speedup is 7.33
[35mHandling overlay: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/overlays/rootfs/async-commit)...[0m
[35mHandling overlay: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/overlays/rootfs/bootanim)...[0m
[35mHandling overlay: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/overlays/rootfs/chromium)...[0m
[35mInstalling overlay: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/overlays/rootfs/default to /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target...[0m
sending incremental file list
./

sent 53 bytes  received 19 bytes  144.00 bytes/sec
total size is 0  speedup is 0.00
[35mHandling overlay: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/overlays/rootfs/disk-helpers)...[0m
[36mInstalling mount service...[0m
sending incremental file list
usr/
usr/bin/

sent 158 bytes  received 24 bytes  364.00 bytes/sec
total size is 13,953  speedup is 76.66
[35mHandling overlay: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/fonts)...[0m
[35mNo extra fonts for buildroot by default[0m
[35mHandling overlay: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/overlays/rootfs/frecon)...[0m
[35mHandling overlay: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/overlays/rootfs/fstrim)...[0m
[35mHandling overlay: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/overlays/rootfs/generate-logs)...[0m
[35mHandling overlay: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/overlays/rootfs/input-event-daemon)...[0m
[35mDisabling input-event-daemon...[0m
[35mHandling overlay: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/overlays/rootfs/irqbalance)...[0m
[35mHandling overlay: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/overlays/rootfs/log-guardian)...[0m
[35mHandling overlay: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/tools)...[0m
[35mHandling overlay: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/overlays/rootfs/usb-gadget)...[0m
[36mInstalling USB gadget service...[0m
[36mUSB gadget functions: adb[0m
[36mInstalling ADBD...[0m
sending incremental file list
lib/
lib/udev/
lib/udev/rules.d/
usr/bin/
usr/bin/usbdevice

sent 17,391 bytes  received 52 bytes  34,886.00 bytes/sec
total size is 17,268  speedup is 0.99
[35mRunning 91-modules.sh for /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target (buildroot init=busybox)...[0m
[36mStrip kernel modules...[0m
copy from `/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target/usr/lib/module/rockit.ko' [elf64-littleaarch64] to `/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target/usr/lib/module/stutGwi0' [elf64-littleaarch64]
copy from `/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target/usr/lib/module/kmpp.ko' [elf64-littleaarch64] to `/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target/usr/lib/module/st23n8Nu' [elf64-littleaarch64]
[35mRunning 95-extra-parts.sh for /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target (buildroot init=busybox)...[0m
[36mPreparing extra partitions...[0m
[35mRunning 97-ldcache.sh for /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target (buildroot init=busybox)...[0m
[35mRunning 99-owner.sh for /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target (buildroot init=busybox)...[0m

# 2025-08-30 08:26:21
# run hook: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/target

[35mRunning 01-hostname.sh for /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/target (recovery init=busybox)...[0m
[36mSetting hostname: rv1126bp-recovery[0m
[35mRunning 10-os-release.sh for /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/target (recovery init=busybox)...[0m
[36mAdding information to /etc/os-release...[0m
[35mRunning 30-fstab.sh for /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/target (recovery init=busybox)...[0m
[36mFixing up /etc/fstab...[0m
[36mFixing up rootfs type: ext4[0m
[36mFixup basic partitions for non-systemd init...[0m
[36mFixing up basic partition: proc /proc[0m
[36mFixing up basic partition: devtmpfs /dev[0m
[36mFixing up basic partition: devpts /dev/pts mode=0620,ptmxmode=0000,gid=5[0m
[36mFixing up basic partition: tmpfs /dev/shm nosuid,nodev,noexec[0m
[36mFixing up basic partition: sysfs /sys nosuid,nodev,noexec[0m
[36mFixing up basic partition: configfs /sys/kernel/config[0m
[36mFixing up basic partition: debugfs /sys/kernel/debug[0m
[36mFixing up basic partition: pstore /sys/fs/pstore nosuid,nodev,noexec[0m
[36mFixing up device partition: /dev/sda1 /mnt/udisk auto[0m
[36mFixing up device partition: /dev/mmcblk1p1 /mnt/sdcard auto[0m
[36mFixing up device partition: PARTLABEL=oem /oem ext4 defaults[0m
[36mFixing up device partition: PARTLABEL=userdata /userdata ext4 defaults[0m
[35mRunning 40-busybox-reboot.sh for /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/target (recovery init=busybox)...[0m
[35mRunning 50-locale.sh for /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/target (recovery init=busybox)...[0m
[36mSetting LANG environment to en_US.UTF-8...[0m
[35mRunning 90-overlay.sh for /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/target (recovery init=busybox)...[0m
[35mHandling overlay: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/overlays/common/default)...[0m
sending incremental file list
./

sent 53 bytes  received 19 bytes  144.00 bytes/sec
total size is 0  speedup is 0.00
[35mInstalling overlay: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/overlays/common/udev-rules to /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/target...[0m
sending incremental file list
lib/
lib/udev/
lib/udev/rules.d/
lib/udev/rules.d/61-persistent-internal-storage.rules
lib/udev/rules.d/88-rockchip-camera.rules
lib/udev/rules.d/99-rockchip-permissions.rules

sent 2,216 bytes  received 85 bytes  4,602.00 bytes/sec
total size is 1,854  speedup is 0.81
[35mHandling overlay: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/overlays/recovery/adbd)...[0m
[35mADBD disabled...[0m
[35mHandling overlay: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/overlays/recovery/default)...[0m
sending incremental file list

sent 46 bytes  received 12 bytes  116.00 bytes/sec
total size is 0  speedup is 0.00
[35mRunning 91-modules.sh for /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/target (recovery init=busybox)...[0m
[36mStrip kernel modules...[0m
[35mRunning 97-ldcache.sh for /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/target (recovery init=busybox)...[0m
[35mRunning 99-owner.sh for /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/target (recovery init=busybox)...[0m

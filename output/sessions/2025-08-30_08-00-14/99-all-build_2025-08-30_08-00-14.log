# 2025-08-30 08:00:14
# run func: build_hook all

[36m==========================================[0m
[36m          Start building all images[0m
[36m==========================================[0m
[35mGenerated blank misc image[0m
[35mDone packing /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/firmware/misc.img[0m
[35mRunning mk-misc.sh - build_misc succeeded.[0m
[36mToolchain for loader (U-Boot):[0m
[36m/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/prebuilts/gcc/linux-x86/aarch64/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-[0m

[36m==========================================[0m
[36m          Start building U-Boot[0m
[36m==========================================[0m
[35m+ cd u-boot[0m
[35m+ ./make.sh CROSS_COMPILE=/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/prebuilts/gcc/linux-x86/aarch64/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu- rv1126b ../rkbin/RKBOOT/RV1126BPMINIALL.ini --spl-new[0m
## make rv1126b_defconfig -j44
#
# configuration written to .config
#
scripts/kconfig/conf  --silentoldconfig Kconfig
  CHK     include/config.h
  CFG     u-boot.cfg
  GEN     include/autoconf.mk.dep
  CFG     spl/u-boot.cfg
  CFG     tpl/u-boot.cfg
  GEN     include/autoconf.mk
  GEN     tpl/include/autoconf.mk
  GEN     spl/include/autoconf.mk
  CHK     include/config/uboot.release
  CHK     include/generated/timestamp_autogenerated.h
  UPD     include/generated/timestamp_autogenerated.h
  CHK     include/generated/version_autogenerated.h
  CHK     include/generated/generic-asm-offsets.h
  CHK     include/generated/asm-offsets.h
  CHK     include/config.h
  CFG     u-boot.cfg
  HOSTCC  tools/mkenvimage.o
  HOSTCC  tools/fit_image.o
  HOSTCC  tools/image-host.o
  HOSTCC  tools/dumpimage.o
  HOSTCC  tools/mkimage.o
  HOSTLD  tools/mkenvimage
  HOSTLD  tools/dumpimage
  HOSTLD  tools/mkimage
  CC      arch/arm/cpu/armv8/fwcall.o
  CC      arch/arm/mach-rockchip/board.o
  CC      common/main.o
  LD      arch/arm/cpu/armv8/built-in.o
  CC      cmd/version.o
  CC      drivers/usb/gadget/f_fastboot.o
  LD      cmd/built-in.o
  LD      common/built-in.o
  CC      lib/display_options.o
  LD      arch/arm/mach-rockchip/built-in.o
  LD      lib/built-in.o
  LD      drivers/usb/gadget/built-in.o
  LD      u-boot
  OBJCOPY u-boot.srec
  OBJCOPY u-boot-nodtb.bin
  SYM     u-boot.sym
start=$(/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/prebuilts/gcc/linux-x86/aarch64/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-nm u-boot | grep __rel_dyn_start | cut -f 1 -d ' '); end=$(/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/prebuilts/gcc/linux-x86/aarch64/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-nm u-boot | grep __rel_dyn_end | cut -f 1 -d ' '); tools/relocate-rela u-boot-nodtb.bin 0x40200000 $start $end
make[2]: “arch/arm/dts/rv1126b-evb.dtb”已是最新。
  COPY    u-boot.dtb
  CAT     u-boot-dtb.bin
  MKIMAGE u-boot.img
  MKIMAGE u-boot-dtb.img
  COPY    u-boot.bin
  ALIGN   u-boot.bin
  COPY    spl/u-boot-spl.dtb
  CC      spl/common/spl/spl.o
  CC      tpl/arch/arm/mach-rockchip/tpl.o
  CC      spl/arch/arm/mach-rockchip/spl.o
  CC      spl/arch/arm/cpu/armv8/fwcall.o
  CC      tpl/arch/arm/cpu/armv8/fwcall.o
  CC      spl/lib/display_options.o
  LD      tpl/arch/arm/cpu/armv8/built-in.o
  LD      spl/arch/arm/cpu/armv8/built-in.o
  LD      tpl/arch/arm/mach-rockchip/built-in.o
  LD      spl/arch/arm/mach-rockchip/built-in.o
  LD      spl/common/spl/built-in.o
  LD      spl/lib/built-in.o
  LD      tpl/u-boot-tpl
  OBJCOPY tpl/u-boot-tpl-nodtb.bin
  COPY    tpl/u-boot-tpl.bin
  LD      spl/u-boot-spl
  OBJCOPY spl/u-boot-spl-nodtb.bin
  CAT     spl/u-boot-spl-dtb.bin
  COPY    spl/u-boot-spl.bin
  CFGCHK  u-boot.cfg
pack u-boot.itb okay! Input: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/rkbin/RKTRUST/RV1126BTRUST.ini

FIT description: FIT Image with ATF/OP-TEE/U-Boot/MCU
Created:         Sat Aug 30 08:00:17 2025
 Image 0 (uboot)
  Description:  U-Boot
  Created:      Sat Aug 30 08:00:17 2025
  Type:         Standalone Program
  Compression:  lzma compressed
  Data Size:    350148 Bytes = 341.94 KiB = 0.33 MiB
  Architecture: AArch64
  Load Address: 0x40200000
  Entry Point:  unavailable
  Hash algo:    sha256
  Hash value:   a935b02a5b68dd57243542d9abd6c502e8780963ee8bca2e5585d84b13c43860
 Image 1 (atf-1)
  Description:  ARM Trusted Firmware
  Created:      Sat Aug 30 08:00:17 2025
  Type:         Firmware
  Compression:  lzma compressed
  Data Size:    33662 Bytes = 32.87 KiB = 0.03 MiB
  Architecture: AArch64
  Load Address: 0x40000000
  Hash algo:    sha256
  Hash value:   42352af0db7a55a3a46d92d23bf9db03a74ce91bcd91070b0b12449fd326585c
 Image 2 (atf-2)
  Description:  ARM Trusted Firmware
  Created:      Sat Aug 30 08:00:17 2025
  Type:         Firmware
  Compression:  uncompressed
  Data Size:    8192 Bytes = 8.00 KiB = 0.01 MiB
  Architecture: AArch64
  Load Address: 0x3ffbb000
  Hash algo:    sha256
  Hash value:   8736494db579a3e6fcce3be9666f40885e99cc0775c66042736b2b1d70564942
 Image 3 (atf-3)
  Description:  ARM Trusted Firmware
  Created:      Sat Aug 30 08:00:17 2025
  Type:         Firmware
  Compression:  uncompressed
  Data Size:    6764 Bytes = 6.61 KiB = 0.01 MiB
  Architecture: AArch64
  Load Address: 0x3ff1e000
  Hash algo:    sha256
  Hash value:   c3d9e784a50bcf5e860355fe03eb9293f81ffcb010e89ec4ea7f6a70ea793fb3
 Image 4 (atf-4)
  Description:  ARM Trusted Firmware
  Created:      Sat Aug 30 08:00:17 2025
  Type:         Firmware
  Compression:  uncompressed
  Data Size:    4096 Bytes = 4.00 KiB = 0.00 MiB
  Architecture: AArch64
  Load Address: 0x3ffbd000
  Hash algo:    sha256
  Hash value:   8cb1ae2802d90197b9d860fbbca838237a7e67b00d220f9ac3970c4af8e1eb7c
 Image 5 (fdt)
  Description:  U-Boot dtb
  Created:      Sat Aug 30 08:00:17 2025
  Type:         Flat Device Tree
  Compression:  uncompressed
  Data Size:    7585 Bytes = 7.41 KiB = 0.01 MiB
  Architecture: AArch64
  Hash algo:    sha256
  Hash value:   bc038548e056a130b3b176015c2fd5fba6301121cc627a5e5a34c93ca0e8705e
 Default Configuration: 'conf'
 Configuration 0 (conf)
  Description:  rv1126b-evb
  Kernel:       unavailable
  Firmware:     atf-1
  FDT:          fdt
  Loadables:    uboot
                atf-2
                atf-3
                atf-4
********boot_merger ver 1.35********
Info:Pack loader ok.
pack loader(SPL) okay! Input: ../rkbin/RKBOOT/RV1126BPMINIALL.ini

/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/u-boot
pack loader with new: spl/u-boot-spl.bin

Image(no-signed, version=0): uboot.img (FIT with uboot, trust...) is ready
Image(no-signed): rv1126bp_spl_loader_v1.03.103.bin (with spl, ddr...) is ready
pack uboot.img okay! Input: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/rkbin/RKTRUST/RV1126BTRUST.ini

Platform RV1126B is build OK, with new .config(make rv1126b_defconfig -j44)
/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/prebuilts/gcc/linux-x86/aarch64/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-
Sat Aug 30 08:00:17 CST 2025
[35m+ cd ..[0m
[35mRunning mk-loader.sh - build_uboot succeeded.[0m
[35mUsing kernel version(6.1) from environment[0m
[36mToolchain for kernel:[0m
[36m/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/prebuilts/gcc/linux-x86/aarch64/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-[0m

[36m==========================================[0m
[36m          Start building kernel[0m
[36m==========================================[0m
[36m# Found kernel's basic config fragment: rv1126b.config[0m
[35m+ make -C /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/kernel/ -j23 CROSS_COMPILE=/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/prebuilts/gcc/linux-x86/aarch64/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu- ARCH=arm64 rv1126b_defconfig rv1126b.config[0m
make: Entering directory '/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/kernel-6.1'
#
# configuration written to .config
#
Using .config as base
Merging ./arch/arm64/configs/rv1126b.config
Value of CONFIG_ROCKCHIP_RKNPU_PROC_FS is redefined by fragment ./arch/arm64/configs/rv1126b.config:
Previous value: # CONFIG_ROCKCHIP_RKNPU_PROC_FS is not set
New value: CONFIG_ROCKCHIP_RKNPU_PROC_FS=y

Value of CONFIG_ROCKCHIP_RKNPU_DMA_HEAP is redefined by fragment ./arch/arm64/configs/rv1126b.config:
Previous value: # CONFIG_ROCKCHIP_RKNPU_DMA_HEAP is not set
New value: CONFIG_ROCKCHIP_RKNPU_DMA_HEAP=y

#
# merged configuration written to .config (needs make)
#
.config:5820:warning: override: ROCKCHIP_RKNPU_DMA_HEAP changes choice state
#
# configuration written to .config
#
make: Leaving directory '/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/kernel-6.1'
[35m+ make -C /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/kernel/ -j23 CROSS_COMPILE=/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/prebuilts/gcc/linux-x86/aarch64/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu- ARCH=arm64 rv1126bp-evb-v14.img[0m
make: Entering directory '/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/kernel-6.1'
  SYNC    include/config/auto.conf.cmd
  CALL    scripts/checksyscalls.sh
  Image:  resource.img (with rv1126bp-evb-v14.dtb logo.bmp logo_kernel.bmp) is ready
  Image:  boot.img (with Image  resource.img) is ready
  Image:  zboot.img (with Image.lz4  resource.img) is ready
make: Leaving directory '/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/kernel-6.1'
[35m+ /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/scripts/mk-fitimage.sh kernel/boot.img /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/.chip/boot.its kernel/arch/arm64/boot/Image kernel/arch/arm64/boot/dts/rockchip/rv1126bp-evb-v14.dtb kernel/resource.img[0m
FIT description: U-Boot FIT source file for arm
Created:         Sat Aug 30 08:00:22 2025
 Image 0 (fdt)
  Description:  unavailable
  Created:      Sat Aug 30 08:00:22 2025
  Type:         Flat Device Tree
  Compression:  uncompressed
  Data Size:    68490 Bytes = 66.88 KiB = 0.07 MiB
  Architecture: AArch64
  Load Address: 0xffffff00
  Hash algo:    sha256
  Hash value:   1c05afdd29fb6e150db47a6b11abacaa978ceda0bc748ae9806164800c1c8046
 Image 1 (kernel)
  Description:  unavailable
  Created:      Sat Aug 30 08:00:22 2025
  Type:         Kernel Image
  Compression:  uncompressed
  Data Size:    13174792 Bytes = 12866.01 KiB = 12.56 MiB
  Architecture: AArch64
  OS:           Linux
  Load Address: 0xffffff01
  Entry Point:  0xffffff01
  Hash algo:    sha256
  Hash value:   055cd25c789f3177debc87969b445affdf43e6c4e9c24eb484e01384f8e24d3d
 Image 2 (resource)
  Description:  unavailable
  Created:      Sat Aug 30 08:00:22 2025
  Type:         Multi-File Image
  Compression:  uncompressed
  Data Size:    106496 Bytes = 104.00 KiB = 0.10 MiB
  Hash algo:    sha256
  Hash value:   743c8ff7c5d3c15703691aec25def63213e2903a93b59586b23c7605223e3db3
 Default Configuration: 'conf'
 Configuration 0 (conf)
  Description:  unavailable
  Kernel:       kernel
  FDT:          fdt
[35m+ ln -rsf kernel/boot.img /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/firmware/boot.img[0m
Not Found io-domains in kernel/arch/arm64/boot/dts/rockchip/rv1126bp-evb-v14.dts
[35mRunning mk-kernel.sh - build_kernel succeeded.[0m
[36m==========================================[0m
[36m          Start building rootfs(buildroot)[0m
[36m==========================================[0m
[36m==========================================[0m
[36m          Start building buildroot(2024.02)[0m
[36m==========================================[0m
[35m[0mmake: Entering directory '/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot'
  GEN     /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/Makefile
Parsing defconfig: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/configs/rockchip_rv1126b_ipc_defconfig
Using configs/rockchip/base/kernel.config as base
Merging configs/rockchip/fs/e2fs.config
Merging configs/rockchip/base/common.config
Merging configs/rockchip/base/base.config
Merging configs/rockchip/wifibt/bt.config
Merging configs/rockchip/network/network.config
Merging configs/rockchip/wifibt/wireless.config
Merging configs/rockchip/chips/rv1126b.config
Value of BR2_ROOTFS_OVERLAY is redefined by configs/rockchip/chips/rv1126b.config:
Previous value:	BR2_ROOTFS_OVERLAY="board/rockchip/common/base"
Modify value:	BR2_ROOTFS_OVERLAY+="board/rockchip/rv1126b/fs-overlay/"
New value:	BR2_ROOTFS_OVERLAY="board/rockchip/common/base board/rockchip/rv1126b/fs-overlay/"

Merging configs/rockchip/chips/rv1126b_aarch64.config
Merging configs/rockchip/multimedia/mpp.config
Merging configs/rockchip/multimedia/audio.config
Merging configs/rockchip/multimedia/camera.config
Merging /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/configs/rockchip_rv1126b_ipc_defconfig
Value of BR2_PACKAGE_ALSA_UCM_CONF is redefined by /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/configs/rockchip_rv1126b_ipc_defconfig:
Previous value:	BR2_PACKAGE_ALSA_UCM_CONF=y
Modify value:	# BR2_PACKAGE_ALSA_UCM_CONF is reset to default
New value:	

Value of BR2_PACKAGE_ALSA_UTILS is redefined by /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/configs/rockchip_rv1126b_ipc_defconfig:
Previous value:	BR2_PACKAGE_ALSA_UTILS=y
Modify value:	# BR2_PACKAGE_ALSA_UTILS is reset to default
New value:	

Value of BR2_PACKAGE_LIBMAD is redefined by /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/configs/rockchip_rv1126b_ipc_defconfig:
Previous value:	BR2_PACKAGE_LIBMAD=y
Modify value:	# BR2_PACKAGE_LIBMAD is reset to default
New value:	

Value of BR2_PACKAGE_PULSEAUDIO is redefined by /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/configs/rockchip_rv1126b_ipc_defconfig:
Previous value:	BR2_PACKAGE_PULSEAUDIO=y
Modify value:	# BR2_PACKAGE_PULSEAUDIO is reset to default
New value:	

Value of BR2_ROOTFS_OVERLAY is redefined by /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/configs/rockchip_rv1126b_ipc_defconfig:
Previous value:	BR2_ROOTFS_OVERLAY="board/rockchip/common/base board/rockchip/rv1126b/fs-overlay/"
New value:	BR2_ROOTFS_OVERLAY="board/rockchip/common/base board/rockchip/rv1126b/fs-overlay-ipc/"

#
# merged configuration written to /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/.config.in (needs make)
#
#
# configuration written to /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/.config
#
make: Leaving directory '/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot'
2025-08-30T08:00:29 [7m>>> rockit  Installing to staging directory[27m
2025-08-30T08:00:29 [7m>>> rockit  Fixing libtool files[27m
2025-08-30T08:00:29 [7m>>> rockit  Installing to target[27m
2025-08-30T08:00:29 [7m>>> wpa_supplicant 2.10 Downloading[27m
2025-08-30T08:00:31 [7m>>> wpa_supplicant 2.10 Extracting[27m
2025-08-30T08:00:31 [7m>>> wpa_supplicant 2.10 Patching[27m
2025-08-30T08:00:31 [7m>>> wpa_supplicant 2.10 Configuring[27m
2025-08-30T08:00:31 [7m>>> wpa_supplicant 2.10 Building[27m
2025-08-30T08:00:37 [7m>>> wpa_supplicant 2.10 Installing to staging directory[27m
2025-08-30T08:00:37 [7m>>> wpa_supplicant 2.10 Fixing libtool files[27m
2025-08-30T08:00:37 [7m>>> wpa_supplicant 2.10 Installing to target[27m
2025-08-30T08:00:37 [7m>>> rkipc  Syncing from source dir /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/../app/rkipc[27m
2025-08-30T08:00:37 [7m>>> rkipc  Configuring[27m
2025-08-30T08:00:38 [7m>>> rkipc  Building[27m
2025-08-30T08:00:40 [7m>>> rkipc  Installing to target[27m
2025-08-30T08:00:40 [7m>>> samples  Syncing from source dir /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/../external/samples[27m
2025-08-30T08:00:40 [7m>>> samples  Configuring[27m
2025-08-30T08:00:40 [7m>>> samples  Building[27m
2025-08-30T08:00:51 [7m>>> samples  Installing to target[27m
2025-08-30T08:00:51 [7m>>> ipc_drv_ko  Syncing from source dir /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/../external/ipc_drv_ko[27m
2025-08-30T08:00:51 [7m>>> ipc_drv_ko  Configuring[27m
2025-08-30T08:00:51 [7m>>> ipc_drv_ko  Building[27m
2025-08-30T08:00:51 [7m>>> ipc_drv_ko  Installing to staging directory[27m
2025-08-30T08:00:51 [7m>>> ipc_drv_ko  Fixing libtool files[27m
2025-08-30T08:00:52 [7m>>> ipc_drv_ko  Installing to target[27m
2025-08-30T08:00:52 [7m>>> rkscript  Extracting[27m
2025-08-30T08:00:52 [7m>>> rkscript  Patching[27m
2025-08-30T08:00:52 [7m>>> rkscript  Configuring[27m
2025-08-30T08:00:52 [7m>>> rkscript  Building[27m
2025-08-30T08:00:52 [7m>>> rkscript  Installing to target[27m
2025-08-30T08:00:52 [7m>>> rktoolkit master Syncing from source dir /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/../external/rktoolkit[27m
2025-08-30T08:00:52 [7m>>> rktoolkit master Configuring[27m
2025-08-30T08:00:52 [7m>>> rktoolkit master Building[27m
2025-08-30T08:00:53 [7m>>> rktoolkit master Installing to target[27m
2025-08-30T08:00:53 [7m>>> rkwifibt-app  Syncing from source dir /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/../external/rkwifibt-app[27m
2025-08-30T08:00:53 [7m>>> rkwifibt-app  Configuring[27m
2025-08-30T08:00:54 [7m>>> rkwifibt-app  Building[27m
2025-08-30T08:00:54 [7m>>> rkwifibt-app  Installing to staging directory[27m
2025-08-30T08:00:54 [7m>>> rkwifibt-app  Fixing libtool files[27m
2025-08-30T08:00:54 [7m>>> rkwifibt-app  Installing to target[27m
2025-08-30T08:00:54 [7m>>> rkwifibt 1.0.0 Syncing from source dir /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/../external/rkwifibt[27m
2025-08-30T08:00:56 [7m>>> rkwifibt 1.0.0 Configuring[27m
2025-08-30T08:00:56 [7m>>> rkwifibt 1.0.0 Building[27m
2025-08-30T08:00:56 [7m>>> rkwifibt 1.0.0 Installing to target[27m
2025-08-30T08:00:57 [7m>>> rockchip-alsa-config 1.0 Syncing from source dir /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/../external/alsa-config[27m
2025-08-30T08:00:57 [7m>>> rockchip-alsa-config 1.0 Configuring[27m
2025-08-30T08:00:57 [7m>>> rockchip-alsa-config 1.0 Building[27m
2025-08-30T08:00:57 [7m>>> rockchip-alsa-config 1.0 Installing to target[27m
2025-08-30T08:00:58 [7m>>> rockchip-test master Syncing from source dir /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/../external/rockchip-test[27m
2025-08-30T08:00:58 [7m>>> rockchip-test master Configuring[27m
2025-08-30T08:00:58 [7m>>> rockchip-test master Building[27m
2025-08-30T08:00:58 [7m>>> rockchip-test master Installing to target[27m
2025-08-30T08:00:58 [7m>>> host-gzip 1.13 Downloading[27m
2025-08-30T08:01:00 [7m>>> host-gzip 1.13 Extracting[27m
2025-08-30T08:01:00 [7m>>> host-gzip 1.13 Patching[27m
2025-08-30T08:01:00 [7m>>> host-gzip 1.13 Updating config.sub and config.guess[27m
2025-08-30T08:01:00 [7m>>> host-gzip 1.13 Patching libtool[27m
2025-08-30T08:01:00 [7m>>> host-gzip 1.13 Configuring[27m
2025-08-30T08:01:09 [7m>>> host-gzip 1.13 Building[27m
2025-08-30T08:01:10 [7m>>> host-gzip 1.13 Installing to host directory[27m
2025-08-30T08:01:11 [7m>>> sox 7524160b29a476f7e87bc14fddf12d349f9a3c5e Downloading[27m
2025-08-30T08:01:13 [7m>>> sox 7524160b29a476f7e87bc14fddf12d349f9a3c5e Extracting[27m
2025-08-30T08:01:13 [7m>>> sox 7524160b29a476f7e87bc14fddf12d349f9a3c5e Patching[27m
2025-08-30T08:01:13 [7m>>> sox 7524160b29a476f7e87bc14fddf12d349f9a3c5e Updating config.sub and config.guess[27m
2025-08-30T08:01:13 [7m>>> sox 7524160b29a476f7e87bc14fddf12d349f9a3c5e Configuring[27m
2025-08-30T08:01:13 [7m>>> sox 7524160b29a476f7e87bc14fddf12d349f9a3c5e Autoreconfiguring[27m
2025-08-30T08:01:18 [7m>>> sox 7524160b29a476f7e87bc14fddf12d349f9a3c5e Patching libtool[27m
2025-08-30T08:01:21 [7m>>> sox 7524160b29a476f7e87bc14fddf12d349f9a3c5e Building[27m
2025-08-30T08:01:24 [7m>>> sox 7524160b29a476f7e87bc14fddf12d349f9a3c5e Installing to staging directory[27m
2025-08-30T08:01:24 [7m>>> sox 7524160b29a476f7e87bc14fddf12d349f9a3c5e Fixing libtool files[27m
2025-08-30T08:01:25 [7m>>> sox 7524160b29a476f7e87bc14fddf12d349f9a3c5e Installing to target[27m
2025-08-30T08:01:25 [7m>>> stressapptest 6714c57d0d67f5a2a7a9987791af6729289bf64e Downloading[27m
2025-08-30T08:01:25 [7m>>> stressapptest 6714c57d0d67f5a2a7a9987791af6729289bf64e Extracting[27m
2025-08-30T08:01:25 [7m>>> stressapptest 6714c57d0d67f5a2a7a9987791af6729289bf64e Patching[27m
2025-08-30T08:01:25 [7m>>> stressapptest 6714c57d0d67f5a2a7a9987791af6729289bf64e Updating config.sub and config.guess[27m
2025-08-30T08:01:25 [7m>>> stressapptest 6714c57d0d67f5a2a7a9987791af6729289bf64e Patching libtool[27m
2025-08-30T08:01:25 [7m>>> stressapptest 6714c57d0d67f5a2a7a9987791af6729289bf64e Configuring[27m
2025-08-30T08:01:29 [7m>>> stressapptest 6714c57d0d67f5a2a7a9987791af6729289bf64e Building[27m
2025-08-30T08:01:31 [7m>>> stressapptest 6714c57d0d67f5a2a7a9987791af6729289bf64e Installing to target[27m
2025-08-30T08:01:32 [7m>>> host-zic 2023c Downloading[27m
2025-08-30T08:01:33 [7m>>> host-zic 2023c Extracting[27m
2025-08-30T08:01:33 [7m>>> host-zic 2023c Patching[27m
2025-08-30T08:01:33 [7m>>> host-zic 2023c Configuring[27m
2025-08-30T08:01:33 [7m>>> host-zic 2023c Building[27m
2025-08-30T08:01:33 [7m>>> host-zic 2023c Installing to host directory[27m
2025-08-30T08:01:34 [7m>>> host-tzdata 2023c Downloading[27m
2025-08-30T08:01:36 [7m>>> host-tzdata 2023c Extracting[27m
2025-08-30T08:01:36 [7m>>> host-tzdata 2023c Patching[27m
2025-08-30T08:01:36 [7m>>> host-tzdata 2023c Configuring[27m
2025-08-30T08:01:36 [7m>>> host-tzdata 2023c Building[27m
2025-08-30T08:01:36 [7m>>> host-tzdata 2023c Installing to host directory[27m
2025-08-30T08:01:37 [7m>>> tzdata 2023c Extracting[27m
2025-08-30T08:01:37 [7m>>> tzdata 2023c Patching[27m
2025-08-30T08:01:37 [7m>>> tzdata 2023c Configuring[27m
2025-08-30T08:01:37 [7m>>> tzdata 2023c Building[27m
2025-08-30T08:01:37 [7m>>> tzdata 2023c Installing to target[27m
2025-08-30T08:01:38 [7m>>> urandom-scripts  Extracting[27m
2025-08-30T08:01:38 [7m>>> urandom-scripts  Patching[27m
2025-08-30T08:01:38 [7m>>> urandom-scripts  Configuring[27m
2025-08-30T08:01:38 [7m>>> urandom-scripts  Building[27m
2025-08-30T08:01:38 [7m>>> urandom-scripts  Installing to target[27m
2025-08-30T08:01:38 [7m>>> usbmount 0.0.22 Downloading[27m
2025-08-30T08:01:39 [7m>>> usbmount 0.0.22 Extracting[27m
2025-08-30T08:01:39 [7m>>> usbmount 0.0.22 Patching[27m
2025-08-30T08:01:39 [7m>>> usbmount 0.0.22 Configuring[27m
2025-08-30T08:01:39 [7m>>> usbmount 0.0.22 Building[27m
2025-08-30T08:01:39 [7m>>> usbmount 0.0.22 Installing to target[27m
2025-08-30T08:01:39 [7m>>> host-lz4 1.9.4 Downloading[27m
2025-08-30T08:01:42 [7m>>> host-lz4 1.9.4 Extracting[27m
2025-08-30T08:01:42 [7m>>> host-lz4 1.9.4 Patching[27m
2025-08-30T08:01:42 [7m>>> host-lz4 1.9.4 Configuring[27m
2025-08-30T08:01:42 [7m>>> host-lz4 1.9.4 Building[27m
2025-08-30T08:01:48 [7m>>> host-lz4 1.9.4 Installing to host directory[27m
2025-08-30T08:01:49 [7m>>> host-lzo 2.10 Downloading[27m
2025-08-30T08:01:50 [7m>>> host-lzo 2.10 Extracting[27m
2025-08-30T08:01:51 [7m>>> host-lzo 2.10 Patching[27m
2025-08-30T08:01:51 [7m>>> host-lzo 2.10 Configuring[27m
2025-08-30T08:01:55 [7m>>> host-lzo 2.10 Building[27m
2025-08-30T08:01:56 [7m>>> host-lzo 2.10 Installing to host directory[27m
2025-08-30T08:01:57 [7m>>> host-zstd 1.5.5 Downloading[27m
2025-08-30T08:01:59 [7m>>> host-zstd 1.5.5 Extracting[27m
2025-08-30T08:01:59 [7m>>> host-zstd 1.5.5 Patching[27m
2025-08-30T08:01:59 [7m>>> host-zstd 1.5.5 Configuring[27m
2025-08-30T08:01:59 [7m>>> host-zstd 1.5.5 Building[27m
2025-08-30T08:02:06 [7m>>> host-zstd 1.5.5 Installing to host directory[27m
2025-08-30T08:02:08 [7m>>> host-squashfs 4.6.1 Downloading[27m
2025-08-30T08:02:09 [7m>>> host-squashfs 4.6.1 Extracting[27m
2025-08-30T08:02:09 [7m>>> host-squashfs 4.6.1 Patching[27m
2025-08-30T08:02:09 [7m>>> host-squashfs 4.6.1 Configuring[27m
2025-08-30T08:02:09 [7m>>> host-squashfs 4.6.1 Building[27m
2025-08-30T08:02:11 [7m>>> host-squashfs 4.6.1 Installing to host directory[27m
2025-08-30T08:02:12 [7m>>>   Finalizing host directory[27m
2025-08-30T08:02:12 [7m>>>   Finalizing target directory[27m
2025-08-30T08:02:12 [7m>>>   Sanitizing RPATH in target tree[27m
2025-08-30T08:02:14 [7m>>>   Sanity check in overlay board/rockchip/common/base[27m
2025-08-30T08:02:14 [7m>>>   Sanity check in overlay board/rockchip/rv1126b/fs-overlay-ipc/[27m
2025-08-30T08:02:14 [7m>>>   Copying overlay board/rockchip/common/base[27m
2025-08-30T08:02:14 [7m>>>   Copying overlay board/rockchip/rv1126b/fs-overlay-ipc/[27m
2025-08-30T08:02:14 [7m>>>   Executing post-build script board/rockchip/common/post-build.sh[27m
2025-08-30T08:02:14 >>> Ignored board/rockchip/common/overlays/10-weston
2025-08-30T08:02:14 >>> Ignored board/rockchip/common/overlays/11-weston-chromium
2025-08-30T08:02:14 >>> Copying board/rockchip/common/overlays/20-wlan0
2025-08-30T08:02:14 >>> Copying board/rockchip/common/overlays/default
2025-08-30T08:02:14 >>> [35mRunning 00-wifibt.sh for /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target (buildroot init=busybox)...[0m
2025-08-30T08:02:14 >>> [36mBuilding Wifi/BT module and firmwares...[0m
2025-08-30T08:02:14 >>> [35mRunning 01-hostname.sh for /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target (buildroot init=busybox)...[0m
2025-08-30T08:02:15 >>> [35mRunning 10-os-release.sh for /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target (buildroot init=busybox)...[0m
2025-08-30T08:02:15 >>> [35mRunning 20-info.sh for /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target (buildroot init=busybox)...[0m
2025-08-30T08:02:15 >>> [35mRunning 30-fstab.sh for /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target (buildroot init=busybox)...[0m
2025-08-30T08:02:15 >>> [35mRunning 40-busybox-reboot.sh for /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target (buildroot init=busybox)...[0m
2025-08-30T08:02:15 >>> [35mRunning 50-locale.sh for /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target (buildroot init=busybox)...[0m
2025-08-30T08:02:15 >>> [35mRunning 90-overlay.sh for /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target (buildroot init=busybox)...[0m
2025-08-30T08:02:15 >>> [35mHandling overlay: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/overlays/common/default)...[0m
2025-08-30T08:02:15 >>> [35mInstalling overlay: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/overlays/common/udev-rules to /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target...[0m
2025-08-30T08:02:15 >>> [35mHandling overlay: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/overlays/rootfs/async-commit)...[0m
2025-08-30T08:02:15 >>> [35mHandling overlay: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/overlays/rootfs/bootanim)...[0m
2025-08-30T08:02:15 >>> [35mHandling overlay: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/overlays/rootfs/chromium)...[0m
2025-08-30T08:02:15 >>> [35mInstalling overlay: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/overlays/rootfs/default to /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target...[0m
2025-08-30T08:02:15 >>> [35mHandling overlay: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/overlays/rootfs/disk-helpers)...[0m
2025-08-30T08:02:15 >>> [36mInstalling mount service...[0m
2025-08-30T08:02:15 >>> [35mHandling overlay: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/fonts)...[0m
2025-08-30T08:02:15 >>> [35mHandling overlay: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/overlays/rootfs/frecon)...[0m
2025-08-30T08:02:15 >>> [35mHandling overlay: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/overlays/rootfs/fstrim)...[0m
2025-08-30T08:02:15 >>> [35mHandling overlay: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/overlays/rootfs/generate-logs)...[0m
2025-08-30T08:02:15 >>> [35mHandling overlay: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/overlays/rootfs/input-event-daemon)...[0m
2025-08-30T08:02:15 >>> [35mHandling overlay: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/overlays/rootfs/irqbalance)...[0m
2025-08-30T08:02:15 >>> [35mHandling overlay: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/overlays/rootfs/log-guardian)...[0m
2025-08-30T08:02:15 >>> [35mHandling overlay: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/tools)...[0m
2025-08-30T08:02:15 >>> [35mHandling overlay: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/overlays/rootfs/usb-gadget)...[0m
2025-08-30T08:02:15 >>> [36mInstalling USB gadget service...[0m
2025-08-30T08:02:15 >>> [36mInstalling ADBD...[0m
2025-08-30T08:02:15 >>> [35mRunning 91-modules.sh for /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target (buildroot init=busybox)...[0m
2025-08-30T08:02:15 >>> [35mRunning 95-extra-parts.sh for /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target (buildroot init=busybox)...[0m
2025-08-30T08:02:15 >>> [35mRunning 97-ldcache.sh for /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target (buildroot init=busybox)...[0m
2025-08-30T08:02:15 >>> [35mRunning 99-owner.sh for /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target (buildroot init=busybox)...[0m
2025-08-30T08:02:15 >>> [35mRunning build.sh - post-rootfs succeeded.[0m
2025-08-30T08:02:15 [7m>>>   Generating root filesystems common tables[27m
2025-08-30T08:02:15 [7m>>>   Generating filesystem image rootfs.cpio[27m
2025-08-30T08:02:32 [7m>>>   Generating filesystem image rootfs.ext2[27m
2025-08-30T08:02:34 [7m>>>   Generating filesystem image rootfs.squashfs[27m
2025-08-30T08:02:37 [7m>>>   Generating filesystem image rootfs.tar[27m
Done in 2min 16s
[35mLog saved on /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/sessions/2025-08-30_08-00-14/br-rockchip_rv1126b_ipc_2025-08-30_08-00-23.log[0m
[35mGenerated images:[0m
/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/buildroot/images/rootfs.cpio
/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/buildroot/images/rootfs.cpio.gz
/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/buildroot/images/rootfs.ext2
/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/buildroot/images/rootfs.ext4
/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/buildroot/images/rootfs.squashfs
/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/buildroot/images/rootfs.tar
# 2025-08-30 08:02:14
# run hook: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target

[35mRunning 00-wifibt.sh for /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target (buildroot init=busybox)...[0m
[36mBuilding Wifi/BT module and firmwares...[0m
[35mSkipping 00-wifibt.sh - build_wifibt for missing configs:  RK_WIFIBT RK_WIFIBT_MODULES.[0m
[35mRunning 01-hostname.sh for /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target (buildroot init=busybox)...[0m
[36mSetting hostname: rv1126bp-buildroot[0m
[35mRunning 10-os-release.sh for /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target (buildroot init=busybox)...[0m
[36mAdding information to /etc/os-release...[0m
[35mRunning 20-info.sh for /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target (buildroot init=busybox)...[0m
[35mRunning 30-fstab.sh for /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target (buildroot init=busybox)...[0m
[36mFixing up /etc/fstab...[0m
[36mFixing up rootfs type: ext4[0m
[36mFixup basic partitions for non-systemd init...[0m
[36mFixing up basic partition: proc /proc[0m
[36mFixing up basic partition: devtmpfs /dev[0m
[36mFixing up basic partition: devpts /dev/pts mode=0620,ptmxmode=0000,gid=5[0m
[36mFixing up basic partition: tmpfs /dev/shm nosuid,nodev,noexec[0m
[36mFixing up basic partition: sysfs /sys nosuid,nodev,noexec[0m
[36mFixing up basic partition: configfs /sys/kernel/config[0m
[36mFixing up basic partition: debugfs /sys/kernel/debug[0m
[36mFixing up basic partition: pstore /sys/fs/pstore nosuid,nodev,noexec[0m
[36mFixing up device partition: PARTLABEL=oem /oem ext4 defaults[0m
[36mFixing up device partition: PARTLABEL=userdata /userdata ext4 defaults[0m
[35mRunning 40-busybox-reboot.sh for /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target (buildroot init=busybox)...[0m
[35mRunning 50-locale.sh for /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target (buildroot init=busybox)...[0m
[36mSetting LANG environment to en_US.UTF-8...[0m
[35mRunning 90-overlay.sh for /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target (buildroot init=busybox)...[0m
[35mHandling overlay: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/overlays/common/default)...[0m
sending incremental file list
./

sent 53 bytes  received 19 bytes  144.00 bytes/sec
total size is 0  speedup is 0.00
[35mInstalling overlay: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/overlays/common/udev-rules to /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target...[0m
sending incremental file list
lib/
lib/udev/
lib/udev/rules.d/
lib/udev/rules.d/61-persistent-internal-storage.rules
lib/udev/rules.d/88-rockchip-camera.rules
lib/udev/rules.d/99-rockchip-permissions.rules

sent 2,216 bytes  received 85 bytes  4,602.00 bytes/sec
total size is 1,854  speedup is 0.81
[35mHandling overlay: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/overlays/rootfs/async-commit)...[0m
[35mHandling overlay: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/overlays/rootfs/bootanim)...[0m
[35mHandling overlay: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/overlays/rootfs/chromium)...[0m
[35mInstalling overlay: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/overlays/rootfs/default to /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target...[0m
sending incremental file list
./

sent 53 bytes  received 19 bytes  144.00 bytes/sec
total size is 0  speedup is 0.00
[35mHandling overlay: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/overlays/rootfs/disk-helpers)...[0m
[36mInstalling mount service...[0m
sending incremental file list
usr/
usr/bin/
usr/bin/disk-helper
usr/bin/mount-helper
usr/bin/resize-helper

sent 14,244 bytes  received 85 bytes  28,658.00 bytes/sec
total size is 13,953  speedup is 0.97
[35mHandling overlay: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/fonts)...[0m
[35mNo extra fonts for buildroot by default[0m
[35mHandling overlay: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/overlays/rootfs/frecon)...[0m
[35mHandling overlay: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/overlays/rootfs/fstrim)...[0m
[35mHandling overlay: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/overlays/rootfs/generate-logs)...[0m
[35mHandling overlay: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/overlays/rootfs/input-event-daemon)...[0m
[35mDisabling input-event-daemon...[0m
[35mHandling overlay: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/overlays/rootfs/irqbalance)...[0m
[35mHandling overlay: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/overlays/rootfs/log-guardian)...[0m
[35mHandling overlay: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/tools)...[0m
[35mHandling overlay: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/overlays/rootfs/usb-gadget)...[0m
[36mInstalling USB gadget service...[0m
[36mUSB gadget functions: adb[0m
[36mInstalling ADBD...[0m
'/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/tools/armhf/adbd' -> '/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target/usr/bin/adbd'
sending incremental file list
lib/
lib/udev/
lib/udev/rules.d/
lib/udev/rules.d/61-usbdevice.rules
usr/bin/
usr/bin/usbdevice

sent 17,562 bytes  received 71 bytes  35,266.00 bytes/sec
total size is 17,268  speedup is 0.98
[35mRunning 91-modules.sh for /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target (buildroot init=busybox)...[0m
[36mStrip kernel modules...[0m
copy from `/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target/usr/lib/module/rockit.ko' [elf64-littleaarch64] to `/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target/usr/lib/module/stcuoBud' [elf64-littleaarch64]
copy from `/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target/usr/lib/module/kmpp.ko' [elf64-littleaarch64] to `/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target/usr/lib/module/stxoSbrh' [elf64-littleaarch64]
[35mRunning 95-extra-parts.sh for /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target (buildroot init=busybox)...[0m
[36mPreparing extra partitions...[0m
[35mRunning 97-ldcache.sh for /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target (buildroot init=busybox)...[0m
[35mRunning 99-owner.sh for /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target (buildroot init=busybox)...[0m
[35mRunning mk-rootfs.sh - build_buildroot /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/buildroot/images succeeded.[0m
[35mRunning mk-rootfs.sh - build_rootfs succeeded.[0m
[36m==========================================[0m
[36m          Start building recovery(buildroot)[0m
[36m==========================================[0m
[35m[0mmake: Entering directory '/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot'
mkdir -p /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/build/buildroot-config/lxdialog
PKG_CONFIG_PATH="" make CC="/usr/bin/gcc" HOSTCC="/usr/bin/gcc" \
    obj=/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/build/buildroot-config -C support/kconfig -f Makefile.br conf
/usr/bin/gcc -I/usr/include/ncursesw -DCURSES_LOC="<curses.h>"  -DNCURSES_WIDECHAR=1 -DLOCALE  -I/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/build/buildroot-config -DCONFIG_=\"\"  -MM *.c > /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/build/buildroot-config/.depend 2>/dev/null || :
/usr/bin/gcc -I/usr/include/ncursesw -DCURSES_LOC="<curses.h>"  -DNCURSES_WIDECHAR=1 -DLOCALE  -I/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/build/buildroot-config -DCONFIG_=\"\"   -c conf.c -o /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/build/buildroot-config/conf.o
/usr/bin/gcc -I/usr/include/ncursesw -DCURSES_LOC="<curses.h>"  -DNCURSES_WIDECHAR=1 -DLOCALE  -I/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/build/buildroot-config -DCONFIG_=\"\"  -I. -c /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/build/buildroot-config/zconf.tab.c -o /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/build/buildroot-config/zconf.tab.o
In file included from /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/build/buildroot-config/zconf.tab.c:2484:
./util.c: In function 'file_write_dep':
./util.c:86:26: warning: '%s' directive writing 10 or more bytes into a region of size between 1 and 4097 [-Wformat-overflow=]
   86 |         sprintf(buf2, "%s%s", dir, name);
      |                          ^~
./util.c:86:9: note: 'sprintf' output 11 or more bytes (assuming 4107) into a destination of size 4097
   86 |         sprintf(buf2, "%s%s", dir, name);
      |         ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/usr/bin/gcc -I/usr/include/ncursesw -DCURSES_LOC="<curses.h>"  -DNCURSES_WIDECHAR=1 -DLOCALE  -I/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/build/buildroot-config -DCONFIG_=\"\"   /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/build/buildroot-config/conf.o /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/build/buildroot-config/zconf.tab.o  -o /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/build/buildroot-config/conf
rm /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/build/buildroot-config/zconf.tab.c
  GEN     /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/Makefile
Parsing defconfig: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/configs/rockchip_rv1126b_recovery_defconfig
Using configs/rockchip/base/kernel.config as base
Merging configs/rockchip/fs/e2fs.config
Merging configs/rockchip/base/common.config
Merging configs/rockchip/base/base.config
Merging configs/rockchip/base/kernel.config
Merging configs/rockchip/fs/e2fs.config
Merging configs/rockchip/base/common.config
Merging configs/rockchip/fs/vfat.config
Merging configs/rockchip/base/recovery.config
Merging configs/rockchip/chips/rv1126b.config
Value of BR2_ROOTFS_OVERLAY is redefined by configs/rockchip/chips/rv1126b.config:
Previous value:	BR2_ROOTFS_OVERLAY="board/rockchip/common/base"
Modify value:	BR2_ROOTFS_OVERLAY+="board/rockchip/rv1126b/fs-overlay/"
New value:	BR2_ROOTFS_OVERLAY="board/rockchip/common/base board/rockchip/rv1126b/fs-overlay/"

Merging configs/rockchip/chips/rv1126b_aarch64.config
Merging /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/configs/rockchip_rv1126b_recovery_defconfig
#
# merged configuration written to /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/.config.in (needs make)
#
#
# configuration written to /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/.config
#
make: Leaving directory '/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot'
2025-08-30T08:02:48 [7m>>> host-skeleton  Extracting[27m
2025-08-30T08:02:48 [7m>>> host-skeleton  Patching[27m
2025-08-30T08:02:48 [7m>>> host-skeleton  Configuring[27m
2025-08-30T08:02:48 [7m>>> host-skeleton  Building[27m
2025-08-30T08:02:48 [7m>>> host-skeleton  Installing to host directory[27m
2025-08-30T08:02:48 [7m>>> host-tar 1.35 Extracting[27m
2025-08-30T08:02:48 [7m>>> host-tar 1.35 Patching[27m
2025-08-30T08:02:48 [7m>>> host-tar 1.35 Updating config.sub and config.guess[27m
2025-08-30T08:02:49 [7m>>> host-tar 1.35 Patching libtool[27m
2025-08-30T08:02:49 [7m>>> host-tar 1.35 Configuring[27m
2025-08-30T08:03:13 [7m>>> host-tar 1.35 Building[27m
2025-08-30T08:03:16 [7m>>> host-tar 1.35 Installing to host directory[27m
2025-08-30T08:03:16 [7m>>> host-xz 5.4.5 Extracting[27m
2025-08-30T08:03:17 [7m>>> host-xz 5.4.5 Patching[27m
2025-08-30T08:03:17 [7m>>> host-xz 5.4.5 Updating config.sub and config.guess[27m
2025-08-30T08:03:17 [7m>>> host-xz 5.4.5 Patching libtool[27m
2025-08-30T08:03:17 [7m>>> host-xz 5.4.5 Configuring[27m
2025-08-30T08:03:23 [7m>>> host-xz 5.4.5 Building[27m
2025-08-30T08:03:26 [7m>>> host-xz 5.4.5 Installing to host directory[27m
2025-08-30T08:03:26 [7m>>> host-m4 1.4.19 Extracting[27m
2025-08-30T08:03:26 [7m>>> host-m4 1.4.19 Patching[27m
2025-08-30T08:03:26 [7m>>> host-m4 1.4.19 Updating config.sub and config.guess[27m
2025-08-30T08:03:26 [7m>>> host-m4 1.4.19 Patching libtool[27m
2025-08-30T08:03:26 [7m>>> host-m4 1.4.19 Configuring[27m
2025-08-30T08:03:45 [7m>>> host-m4 1.4.19 Building[27m
2025-08-30T08:03:47 [7m>>> host-m4 1.4.19 Installing to host directory[27m
2025-08-30T08:03:48 [7m>>> host-libtool 2.4.6 Extracting[27m
2025-08-30T08:03:48 [7m>>> host-libtool 2.4.6 Patching[27m
2025-08-30T08:03:48 [7m>>> host-libtool 2.4.6 Updating config.sub and config.guess[27m
2025-08-30T08:03:48 [7m>>> host-libtool 2.4.6 Configuring[27m
2025-08-30T08:03:52 [7m>>> host-libtool 2.4.6 Building[27m
2025-08-30T08:03:53 [7m>>> host-libtool 2.4.6 Installing to host directory[27m
2025-08-30T08:03:53 [7m>>> host-autoconf 2.72 Extracting[27m
2025-08-30T08:03:53 [7m>>> host-autoconf 2.72 Patching[27m
2025-08-30T08:03:53 [7m>>> host-autoconf 2.72 Updating config.sub and config.guess[27m
2025-08-30T08:03:53 [7m>>> host-autoconf 2.72 Patching libtool[27m
2025-08-30T08:03:53 [7m>>> host-autoconf 2.72 Configuring[27m
2025-08-30T08:03:55 [7m>>> host-autoconf 2.72 Building[27m
2025-08-30T08:03:55 [7m>>> host-autoconf 2.72 Installing to host directory[27m
2025-08-30T08:03:55 [7m>>> host-automake 1.16.5 Extracting[27m
2025-08-30T08:03:55 [7m>>> host-automake 1.16.5 Patching[27m
2025-08-30T08:03:55 [7m>>> host-automake 1.16.5 Updating config.sub and config.guess[27m
2025-08-30T08:03:55 [7m>>> host-automake 1.16.5 Patching libtool[27m
2025-08-30T08:03:55 [7m>>> host-automake 1.16.5 Configuring[27m
2025-08-30T08:03:56 [7m>>> host-automake 1.16.5 Building[27m
2025-08-30T08:03:57 [7m>>> host-automake 1.16.5 Installing to host directory[27m
2025-08-30T08:03:57 [7m>>> host-attr 2.5.2 Extracting[27m
2025-08-30T08:03:57 [7m>>> host-attr 2.5.2 Patching[27m
2025-08-30T08:03:57 [7m>>> host-attr 2.5.2 Updating config.sub and config.guess[27m
2025-08-30T08:03:57 [7m>>> host-attr 2.5.2 Configuring[27m
2025-08-30T08:03:57 [7m>>> host-attr 2.5.2 Autoreconfiguring[27m
2025-08-30T08:04:00 [7m>>> host-attr 2.5.2 Patching libtool[27m
2025-08-30T08:04:02 [7m>>> host-attr 2.5.2 Building[27m
2025-08-30T08:04:02 [7m>>> host-attr 2.5.2 Installing to host directory[27m
2025-08-30T08:04:02 [7m>>> host-acl 2.3.2 Extracting[27m
2025-08-30T08:04:02 [7m>>> host-acl 2.3.2 Patching[27m
2025-08-30T08:04:02 [7m>>> host-acl 2.3.2 Updating config.sub and config.guess[27m
2025-08-30T08:04:02 [7m>>> host-acl 2.3.2 Patching libtool[27m
2025-08-30T08:04:02 [7m>>> host-acl 2.3.2 Configuring[27m
2025-08-30T08:04:04 [7m>>> host-acl 2.3.2 Building[27m
2025-08-30T08:04:05 [7m>>> host-acl 2.3.2 Installing to host directory[27m
2025-08-30T08:04:05 [7m>>> host-fakeroot 1.32.1 Extracting[27m
2025-08-30T08:04:06 [7m>>> host-fakeroot 1.32.1 Patching[27m
2025-08-30T08:04:06 [7m>>> host-fakeroot 1.32.1 Updating config.sub and config.guess[27m
2025-08-30T08:04:06 [7m>>> host-fakeroot 1.32.1 Patching libtool[27m
2025-08-30T08:04:06 [7m>>> host-fakeroot 1.32.1 Configuring[27m
2025-08-30T08:04:10 [7m>>> host-fakeroot 1.32.1 Building[27m
2025-08-30T08:04:10 [7m>>> host-fakeroot 1.32.1 Installing to host directory[27m
2025-08-30T08:04:10 [7m>>> host-makedevs  Extracting[27m
2025-08-30T08:04:10 [7m>>> host-makedevs  Patching[27m
2025-08-30T08:04:10 [7m>>> host-makedevs  Configuring[27m
2025-08-30T08:04:11 [7m>>> host-makedevs  Building[27m
2025-08-30T08:04:11 [7m>>> host-makedevs  Installing to host directory[27m
2025-08-30T08:04:11 [7m>>> host-mkpasswd  Extracting[27m
2025-08-30T08:04:11 [7m>>> host-mkpasswd  Patching[27m
2025-08-30T08:04:11 [7m>>> host-mkpasswd  Configuring[27m
2025-08-30T08:04:11 [7m>>> host-mkpasswd  Building[27m
2025-08-30T08:04:11 [7m>>> host-mkpasswd  Installing to host directory[27m
2025-08-30T08:04:11 [7m>>> host-bison 3.8.2 Extracting[27m
2025-08-30T08:04:11 [7m>>> host-bison 3.8.2 Patching[27m
2025-08-30T08:04:11 [7m>>> host-bison 3.8.2 Updating config.sub and config.guess[27m
2025-08-30T08:04:12 [7m>>> host-bison 3.8.2 Patching libtool[27m
2025-08-30T08:04:12 [7m>>> host-bison 3.8.2 Configuring[27m
2025-08-30T08:04:27 [7m>>> host-bison 3.8.2 Building[27m
2025-08-30T08:04:39 [7m>>> host-bison 3.8.2 Installing to host directory[27m
2025-08-30T08:04:39 [7m>>> host-gawk 5.3.0 Extracting[27m
2025-08-30T08:04:40 [7m>>> host-gawk 5.3.0 Patching[27m
2025-08-30T08:04:40 [7m>>> host-gawk 5.3.0 Updating config.sub and config.guess[27m
2025-08-30T08:04:40 [7m>>> host-gawk 5.3.0 Patching libtool[27m
2025-08-30T08:04:40 [7m>>> host-gawk 5.3.0 Configuring[27m
2025-08-30T08:04:46 [7m>>> host-gawk 5.3.0 Building[27m
2025-08-30T08:04:50 [7m>>> host-gawk 5.3.0 Installing to host directory[27m
2025-08-30T08:04:50 [7m>>> host-binutils 2.40 Extracting[27m
2025-08-30T08:04:52 [7m>>> host-binutils 2.40 Patching[27m
2025-08-30T08:04:52 [7m>>> host-binutils 2.40 Updating config.sub and config.guess[27m
2025-08-30T08:04:52 [7m>>> host-binutils 2.40 Patching libtool[27m
2025-08-30T08:04:52 [7m>>> host-binutils 2.40 Configuring[27m
2025-08-30T08:04:54 [7m>>> host-binutils 2.40 Building[27m
2025-08-30T08:05:19 [7m>>> host-binutils 2.40 Installing to host directory[27m
2025-08-30T08:05:19 [7m>>> host-gmp 6.3.0 Extracting[27m
2025-08-30T08:05:20 [7m>>> host-gmp 6.3.0 Patching[27m
2025-08-30T08:05:20 [7m>>> host-gmp 6.3.0 Updating config.sub and config.guess[27m
2025-08-30T08:05:20 [7m>>> host-gmp 6.3.0 Patching libtool[27m
2025-08-30T08:05:20 [7m>>> host-gmp 6.3.0 Configuring[27m
2025-08-30T08:05:29 [7m>>> host-gmp 6.3.0 Building[27m
2025-08-30T08:05:36 [7m>>> host-gmp 6.3.0 Installing to host directory[27m
2025-08-30T08:05:36 [7m>>> host-mpfr 4.1.1 Extracting[27m
2025-08-30T08:05:36 [7m>>> host-mpfr 4.1.1 Patching[27m
2025-08-30T08:05:36 [7m>>> host-mpfr 4.1.1 Updating config.sub and config.guess[27m
2025-08-30T08:05:36 [7m>>> host-mpfr 4.1.1 Patching libtool[27m
2025-08-30T08:05:36 [7m>>> host-mpfr 4.1.1 Configuring[27m
2025-08-30T08:05:40 [7m>>> host-mpfr 4.1.1 Building[27m
2025-08-30T08:05:44 [7m>>> host-mpfr 4.1.1 Installing to host directory[27m
2025-08-30T08:05:44 [7m>>> host-mpc 1.2.1 Extracting[27m
2025-08-30T08:05:44 [7m>>> host-mpc 1.2.1 Patching[27m
2025-08-30T08:05:44 [7m>>> host-mpc 1.2.1 Updating config.sub and config.guess[27m
2025-08-30T08:05:44 [7m>>> host-mpc 1.2.1 Patching libtool[27m
2025-08-30T08:05:44 [7m>>> host-mpc 1.2.1 Configuring[27m
2025-08-30T08:05:46 [7m>>> host-mpc 1.2.1 Building[27m
2025-08-30T08:05:47 [7m>>> host-mpc 1.2.1 Installing to host directory[27m
2025-08-30T08:05:48 [7m>>> host-gcc-initial 13.3.0 Extracting[27m
2025-08-30T08:05:55 [7m>>> host-gcc-initial 13.3.0 Patching[27m
2025-08-30T08:05:55 [7m>>> host-gcc-initial 13.3.0 Updating config.sub and config.guess[27m
2025-08-30T08:05:55 [7m>>> host-gcc-initial 13.3.0 Patching libtool[27m
2025-08-30T08:05:55 [7m>>> host-gcc-initial 13.3.0 Configuring[27m
2025-08-30T08:05:57 [7m>>> host-gcc-initial 13.3.0 Building[27m
2025-08-30T08:08:45 [7m>>> host-gcc-initial 13.3.0 Installing to host directory[27m
2025-08-30T08:08:49 [7m>>> host-pkgconf 1.6.3 Extracting[27m
2025-08-30T08:08:49 [7m>>> host-pkgconf 1.6.3 Patching[27m
2025-08-30T08:08:49 [7m>>> host-pkgconf 1.6.3 Updating config.sub and config.guess[27m
2025-08-30T08:08:49 [7m>>> host-pkgconf 1.6.3 Patching libtool[27m
2025-08-30T08:08:49 [7m>>> host-pkgconf 1.6.3 Configuring[27m
2025-08-30T08:08:52 [7m>>> host-pkgconf 1.6.3 Building[27m
2025-08-30T08:08:53 [7m>>> host-pkgconf 1.6.3 Installing to host directory[27m
2025-08-30T08:08:53 [7m>>> host-lzip 1.23 Extracting[27m
2025-08-30T08:08:53 [7m>>> host-lzip 1.23 Patching[27m
2025-08-30T08:08:53 [7m>>> host-lzip 1.23 Configuring[27m
2025-08-30T08:08:54 [7m>>> host-lzip 1.23 Building[27m
2025-08-30T08:08:55 [7m>>> host-lzip 1.23 Installing to host directory[27m
2025-08-30T08:08:55 [7m>>> host-make 4.3 Extracting[27m
2025-08-30T08:08:55 [7m>>> host-make 4.3 Patching[27m
2025-08-30T08:08:55 [7m>>> host-make 4.3 Updating config.sub and config.guess[27m
2025-08-30T08:08:55 [7m>>> host-make 4.3 Patching libtool[27m
2025-08-30T08:08:55 [7m>>> host-make 4.3 Configuring[27m
2025-08-30T08:09:02 [7m>>> host-make 4.3 Building[27m
2025-08-30T08:09:03 [7m>>> host-make 4.3 Installing to host directory[27m
2025-08-30T08:09:04 [7m>>> host-autoconf-archive 2023.02.20 Extracting[27m
2025-08-30T08:09:04 [7m>>> host-autoconf-archive 2023.02.20 Patching[27m
2025-08-30T08:09:04 [7m>>> host-autoconf-archive 2023.02.20 Updating config.sub and config.guess[27m
2025-08-30T08:09:04 [7m>>> host-autoconf-archive 2023.02.20 Patching libtool[27m
2025-08-30T08:09:04 [7m>>> host-autoconf-archive 2023.02.20 Configuring[27m
2025-08-30T08:09:05 [7m>>> host-autoconf-archive 2023.02.20 Building[27m
2025-08-30T08:09:05 [7m>>> host-autoconf-archive 2023.02.20 Installing to host directory[27m
2025-08-30T08:09:06 [7m>>> host-expat 2.6.1 Extracting[27m
2025-08-30T08:09:06 [7m>>> host-expat 2.6.1 Patching[27m
2025-08-30T08:09:06 [7m>>> host-expat 2.6.1 Updating config.sub and config.guess[27m
2025-08-30T08:09:06 [7m>>> host-expat 2.6.1 Patching libtool[27m
2025-08-30T08:09:06 [7m>>> host-expat 2.6.1 Configuring[27m
2025-08-30T08:09:10 [7m>>> host-expat 2.6.1 Building[27m
2025-08-30T08:09:12 [7m>>> host-expat 2.6.1 Installing to host directory[27m
2025-08-30T08:09:12 [7m>>> host-libffi 3.4.4 Extracting[27m
2025-08-30T08:09:12 [7m>>> host-libffi 3.4.4 Patching[27m
2025-08-30T08:09:12 [7m>>> host-libffi 3.4.4 Updating config.sub and config.guess[27m
2025-08-30T08:09:12 [7m>>> host-libffi 3.4.4 Configuring[27m
2025-08-30T08:09:12 [7m>>> host-libffi 3.4.4 Autoreconfiguring[27m
2025-08-30T08:09:16 [7m>>> host-libffi 3.4.4 Patching libtool[27m
2025-08-30T08:09:19 [7m>>> host-libffi 3.4.4 Building[27m
2025-08-30T08:09:20 [7m>>> host-libffi 3.4.4 Installing to host directory[27m
2025-08-30T08:09:20 [7m>>> host-libzlib 1.3.1 Extracting[27m
2025-08-30T08:09:20 [7m>>> host-libzlib 1.3.1 Patching[27m
2025-08-30T08:09:20 [7m>>> host-libzlib 1.3.1 Configuring[27m
2025-08-30T08:09:21 [7m>>> host-libzlib 1.3.1 Building[27m
2025-08-30T08:09:24 [7m>>> host-libzlib 1.3.1 Installing to host directory[27m
2025-08-30T08:09:25 [7m>>> host-zlib  Extracting[27m
2025-08-30T08:09:25 [7m>>> host-zlib  Patching[27m
2025-08-30T08:09:25 [7m>>> host-zlib  Configuring[27m
2025-08-30T08:09:25 [7m>>> host-zlib  Building[27m
2025-08-30T08:09:25 [7m>>> host-zlib  Installing to host directory[27m
2025-08-30T08:09:25 [7m>>> host-libopenssl 3.2.1 Extracting[27m
2025-08-30T08:09:25 [7m>>> host-libopenssl 3.2.1 Patching[27m
2025-08-30T08:09:26 [7m>>> host-libopenssl 3.2.1 Configuring[27m
2025-08-30T08:09:30 [7m>>> host-libopenssl 3.2.1 Building[27m
2025-08-30T08:09:58 [7m>>> host-libopenssl 3.2.1 Installing to host directory[27m
2025-08-30T08:10:00 [7m>>> host-openssl  Extracting[27m
2025-08-30T08:10:00 [7m>>> host-openssl  Patching[27m
2025-08-30T08:10:00 [7m>>> host-openssl  Configuring[27m
2025-08-30T08:10:00 [7m>>> host-openssl  Building[27m
2025-08-30T08:10:00 [7m>>> host-openssl  Installing to host directory[27m
2025-08-30T08:10:01 [7m>>> host-python3 3.11.8 Extracting[27m
2025-08-30T08:10:02 [7m>>> host-python3 3.11.8 Patching[27m
2025-08-30T08:10:02 [7m>>> host-python3 3.11.8 Updating config.sub and config.guess[27m
2025-08-30T08:10:02 [7m>>> host-python3 3.11.8 Configuring[27m
2025-08-30T08:10:02 [7m>>> host-python3 3.11.8 Autoreconfiguring[27m
2025-08-30T08:10:05 [7m>>> host-python3 3.11.8 Patching libtool[27m
2025-08-30T08:10:21 [7m>>> host-python3 3.11.8 Building[27m
2025-08-30T08:10:40 [7m>>> host-python3 3.11.8 Installing to host directory[27m
2025-08-30T08:10:44 [7m>>> skeleton-init-common  Extracting[27m
2025-08-30T08:10:44 [7m>>> skeleton-init-common  Patching[27m
2025-08-30T08:10:44 [7m>>> skeleton-init-common  Configuring[27m
2025-08-30T08:10:44 [7m>>> skeleton-init-common  Building[27m
2025-08-30T08:10:44 [7m>>> skeleton-init-common  Installing to staging directory[27m
2025-08-30T08:10:44 [7m>>> skeleton-init-common  Fixing libtool files[27m
2025-08-30T08:10:44 [7m>>> skeleton-init-common  Installing to target[27m
2025-08-30T08:10:45 [7m>>> skeleton-init-sysv  Extracting[27m
2025-08-30T08:10:45 [7m>>> skeleton-init-sysv  Patching[27m
2025-08-30T08:10:45 [7m>>> skeleton-init-sysv  Configuring[27m
2025-08-30T08:10:45 [7m>>> skeleton-init-sysv  Building[27m
2025-08-30T08:10:45 [7m>>> skeleton-init-sysv  Installing to target[27m
2025-08-30T08:10:45 [7m>>> skeleton  Extracting[27m
2025-08-30T08:10:45 [7m>>> skeleton  Patching[27m
2025-08-30T08:10:45 [7m>>> skeleton  Configuring[27m
2025-08-30T08:10:45 [7m>>> skeleton  Building[27m
2025-08-30T08:10:45 [7m>>> skeleton  Installing to target[27m
2025-08-30T08:10:45 [7m>>> linux-headers custom Syncing from source dir /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/../kernel[27m
2025-08-30T08:10:55 [7m>>> linux-headers custom Configuring[27m
2025-08-30T08:10:56 [7m>>> linux-headers custom Building[27m
2025-08-30T08:10:56 [7m>>> linux-headers custom Installing to staging directory[27m
2025-08-30T08:10:56 [7m>>> linux-headers custom Fixing libtool files[27m
2025-08-30T08:10:56 [7m>>> linux-headers custom Installing to target[27m
2025-08-30T08:10:56 [7m>>> glibc 2.41-5-gcb7f20653724029be89224ed3a35d627cc5b4163 Extracting[27m
2025-08-30T08:10:58 [7m>>> glibc 2.41-5-gcb7f20653724029be89224ed3a35d627cc5b4163 Patching[27m
2025-08-30T08:10:58 [7m>>> glibc 2.41-5-gcb7f20653724029be89224ed3a35d627cc5b4163 Updating config.sub and config.guess[27m
2025-08-30T08:10:58 [7m>>> glibc 2.41-5-gcb7f20653724029be89224ed3a35d627cc5b4163 Patching libtool[27m
2025-08-30T08:10:58 [7m>>> glibc 2.41-5-gcb7f20653724029be89224ed3a35d627cc5b4163 Configuring[27m
2025-08-30T08:11:00 [7m>>> glibc 2.41-5-gcb7f20653724029be89224ed3a35d627cc5b4163 Building[27m
2025-08-30T08:12:16 [7m>>> glibc 2.41-5-gcb7f20653724029be89224ed3a35d627cc5b4163 Installing to staging directory[27m
2025-08-30T08:12:32 [7m>>> glibc 2.41-5-gcb7f20653724029be89224ed3a35d627cc5b4163 Fixing libtool files[27m
2025-08-30T08:12:32 [7m>>> glibc 2.41-5-gcb7f20653724029be89224ed3a35d627cc5b4163 Installing to target[27m
2025-08-30T08:12:33 [7m>>> host-gcc-final 13.3.0 Extracting[27m
2025-08-30T08:12:40 [7m>>> host-gcc-final 13.3.0 Patching[27m
2025-08-30T08:12:40 [7m>>> host-gcc-final 13.3.0 Updating config.sub and config.guess[27m
2025-08-30T08:12:40 [7m>>> host-gcc-final 13.3.0 Patching libtool[27m
2025-08-30T08:12:41 [7m>>> host-gcc-final 13.3.0 Configuring[27m
2025-08-30T08:12:42 [7m>>> host-gcc-final 13.3.0 Building[27m
2025-08-30T08:17:31 [7m>>> host-gcc-final 13.3.0 Installing to host directory[27m
2025-08-30T08:17:35 [7m>>> gcc-final 13.3.0 Extracting[27m
2025-08-30T08:17:41 [7m>>> gcc-final 13.3.0 Patching[27m
2025-08-30T08:17:42 [7m>>> gcc-final 13.3.0 Configuring[27m
2025-08-30T08:17:42 [7m>>> gcc-final 13.3.0 Building[27m
2025-08-30T08:17:42 [7m>>> gcc-final 13.3.0 Installing to staging directory[27m
2025-08-30T08:17:42 [7m>>> gcc-final 13.3.0 Fixing libtool files[27m
2025-08-30T08:17:42 [7m>>> gcc-final 13.3.0 Installing to target[27m
2025-08-30T08:17:42 [7m>>> toolchain-buildroot  Extracting[27m
2025-08-30T08:17:42 [7m>>> toolchain-buildroot  Patching[27m
2025-08-30T08:17:42 [7m>>> toolchain-buildroot  Configuring[27m
2025-08-30T08:17:42 [7m>>> toolchain-buildroot  Building[27m
2025-08-30T08:17:42 [7m>>> toolchain-buildroot  Installing to target[27m
2025-08-30T08:17:42 [7m>>> toolchain  Extracting[27m
2025-08-30T08:17:42 [7m>>> toolchain  Patching[27m
2025-08-30T08:17:43 [7m>>> toolchain  Configuring[27m
2025-08-30T08:17:43 [7m>>> toolchain  Building[27m
2025-08-30T08:17:43 [7m>>> toolchain  Installing to staging directory[27m
2025-08-30T08:17:43 [7m>>> toolchain  Fixing libtool files[27m
2025-08-30T08:17:43 [7m>>> toolchain  Installing to target[27m
2025-08-30T08:17:43 [7m>>> host-gperf 3.1 Extracting[27m
2025-08-30T08:17:43 [7m>>> host-gperf 3.1 Patching[27m
2025-08-30T08:17:43 [7m>>> host-gperf 3.1 Updating config.sub and config.guess[27m
2025-08-30T08:17:43 [7m>>> host-gperf 3.1 Patching libtool[27m
2025-08-30T08:17:43 [7m>>> host-gperf 3.1 Configuring[27m
2025-08-30T08:17:45 [7m>>> host-gperf 3.1 Building[27m
2025-08-30T08:17:46 [7m>>> host-gperf 3.1 Installing to host directory[27m
2025-08-30T08:17:46 [7m>>> libzlib 1.3.1 Extracting[27m
2025-08-30T08:17:46 [7m>>> libzlib 1.3.1 Patching[27m
2025-08-30T08:17:46 [7m>>> libzlib 1.3.1 Configuring[27m
2025-08-30T08:17:46 [7m>>> libzlib 1.3.1 Building[27m
2025-08-30T08:17:51 [7m>>> libzlib 1.3.1 Installing to staging directory[27m
2025-08-30T08:17:51 [7m>>> libzlib 1.3.1 Fixing libtool files[27m
2025-08-30T08:17:51 [7m>>> libzlib 1.3.1 Installing to target[27m
2025-08-30T08:17:51 [7m>>> zlib  Extracting[27m
2025-08-30T08:17:51 [7m>>> zlib  Patching[27m
2025-08-30T08:17:51 [7m>>> zlib  Configuring[27m
2025-08-30T08:17:51 [7m>>> zlib  Building[27m
2025-08-30T08:17:51 [7m>>> zlib  Installing to target[27m
2025-08-30T08:17:51 [7m>>> kmod 31 Extracting[27m
2025-08-30T08:17:51 [7m>>> kmod 31 Patching[27m
2025-08-30T08:17:51 [7m>>> kmod 31 Updating config.sub and config.guess[27m
2025-08-30T08:17:51 [7m>>> kmod 31 Patching libtool[27m
2025-08-30T08:17:51 [7m>>> kmod 31 Configuring[27m
2025-08-30T08:17:55 [7m>>> kmod 31 Building[27m
2025-08-30T08:17:56 [7m>>> kmod 31 Installing to staging directory[27m
2025-08-30T08:17:56 [7m>>> kmod 31 Fixing libtool files[27m
2025-08-30T08:17:56 [7m>>> kmod 31 Installing to target[27m
2025-08-30T08:17:57 [7m>>> util-linux-libs 2.39.3 Extracting[27m
2025-08-30T08:17:57 [7m>>> util-linux-libs 2.39.3 Patching[27m
2025-08-30T08:17:57 [7m>>> util-linux-libs 2.39.3 Updating config.sub and config.guess[27m
2025-08-30T08:17:57 [7m>>> util-linux-libs 2.39.3 Patching libtool[27m
2025-08-30T08:17:57 [7m>>> util-linux-libs 2.39.3 Configuring[27m
2025-08-30T08:18:08 [7m>>> util-linux-libs 2.39.3 Building[27m
2025-08-30T08:18:10 [7m>>> util-linux-libs 2.39.3 Installing to staging directory[27m
2025-08-30T08:18:11 [7m>>> util-linux-libs 2.39.3 Fixing libtool files[27m
2025-08-30T08:18:11 [7m>>> util-linux-libs 2.39.3 Installing to target[27m
2025-08-30T08:18:11 [7m>>> eudev 3.2.14 Extracting[27m
2025-08-30T08:18:11 [7m>>> eudev 3.2.14 Patching[27m
2025-08-30T08:18:11 [7m>>> eudev 3.2.14 Updating config.sub and config.guess[27m
2025-08-30T08:18:11 [7m>>> eudev 3.2.14 Patching libtool[27m
2025-08-30T08:18:11 [7m>>> eudev 3.2.14 Configuring[27m
2025-08-30T08:18:18 [7m>>> eudev 3.2.14 Building[27m
2025-08-30T08:18:23 [7m>>> eudev 3.2.14 Installing to staging directory[27m
2025-08-30T08:18:24 [7m>>> eudev 3.2.14 Fixing libtool files[27m
2025-08-30T08:18:24 [7m>>> eudev 3.2.14 Installing to target[27m
2025-08-30T08:18:24 [7m>>> udev  Extracting[27m
2025-08-30T08:18:24 [7m>>> udev  Patching[27m
2025-08-30T08:18:24 [7m>>> udev  Configuring[27m
2025-08-30T08:18:25 [7m>>> udev  Building[27m
2025-08-30T08:18:25 [7m>>> udev  Installing to target[27m
2025-08-30T08:18:25 [7m>>> dosfstools 4.2 Downloading[27m
2025-08-30T08:18:26 [7m>>> dosfstools 4.2 Extracting[27m
2025-08-30T08:18:26 [7m>>> dosfstools 4.2 Patching[27m
2025-08-30T08:18:26 [7m>>> dosfstools 4.2 Updating config.sub and config.guess[27m
2025-08-30T08:18:26 [7m>>> dosfstools 4.2 Patching libtool[27m
2025-08-30T08:18:26 [7m>>> dosfstools 4.2 Configuring[27m
2025-08-30T08:18:28 [7m>>> dosfstools 4.2 Building[27m
2025-08-30T08:18:29 [7m>>> dosfstools 4.2 Installing to target[27m
2025-08-30T08:18:29 [7m>>> util-linux 2.39.3 Extracting[27m
2025-08-30T08:18:30 [7m>>> util-linux 2.39.3 Patching[27m
2025-08-30T08:18:30 [7m>>> util-linux 2.39.3 Updating config.sub and config.guess[27m
2025-08-30T08:18:30 [7m>>> util-linux 2.39.3 Configuring[27m
2025-08-30T08:18:30 [7m>>> util-linux 2.39.3 Autoreconfiguring[27m
2025-08-30T08:18:35 [7m>>> util-linux 2.39.3 Patching libtool[27m
2025-08-30T08:18:46 [7m>>> util-linux 2.39.3 Building[27m
2025-08-30T08:18:51 [7m>>> util-linux 2.39.3 Installing to staging directory[27m
2025-08-30T08:18:52 [7m>>> util-linux 2.39.3 Fixing libtool files[27m
2025-08-30T08:18:52 [7m>>> util-linux 2.39.3 Installing to target[27m
2025-08-30T08:18:52 [7m>>> e2fsprogs 1.47.0 Extracting[27m
2025-08-30T08:18:52 [7m>>> e2fsprogs 1.47.0 Patching[27m
2025-08-30T08:18:52 [7m>>> e2fsprogs 1.47.0 Updating config.sub and config.guess[27m
2025-08-30T08:18:52 [7m>>> e2fsprogs 1.47.0 Patching libtool[27m
2025-08-30T08:18:52 [7m>>> e2fsprogs 1.47.0 Configuring[27m
2025-08-30T08:19:01 [7m>>> e2fsprogs 1.47.0 Building[27m
2025-08-30T08:19:35 [7m>>> e2fsprogs 1.47.0 Installing to staging directory[27m
2025-08-30T08:19:35 [7m>>> e2fsprogs 1.47.0 Fixing libtool files[27m
2025-08-30T08:19:35 [7m>>> e2fsprogs 1.47.0 Installing to target[27m
2025-08-30T08:19:36 [7m>>> busybox 1.36.1 Extracting[27m
2025-08-30T08:19:36 [7m>>> busybox 1.36.1 Patching[27m
2025-08-30T08:19:41 [7m>>> busybox 1.36.1 Configuring[27m
2025-08-30T08:19:41 [7m>>> busybox 1.36.1 Building[27m
2025-08-30T08:19:48 [7m>>> busybox 1.36.1 Installing to target[27m
2025-08-30T08:19:50 [7m>>> bzip2 1.0.8 Downloading[27m
2025-08-30T08:19:52 [7m>>> bzip2 1.0.8 Extracting[27m
2025-08-30T08:19:52 [7m>>> bzip2 1.0.8 Patching[27m
2025-08-30T08:19:52 [7m>>> bzip2 1.0.8 Configuring[27m
2025-08-30T08:19:53 [7m>>> bzip2 1.0.8 Building[27m
2025-08-30T08:19:54 [7m>>> bzip2 1.0.8 Installing to staging directory[27m
2025-08-30T08:19:54 [7m>>> bzip2 1.0.8 Fixing libtool files[27m
2025-08-30T08:19:54 [7m>>> bzip2 1.0.8 Installing to target[27m
2025-08-30T08:19:55 [7m>>> host-util-linux 2.39.3 Extracting[27m
2025-08-30T08:19:55 [7m>>> host-util-linux 2.39.3 Patching[27m
2025-08-30T08:19:55 [7m>>> host-util-linux 2.39.3 Updating config.sub and config.guess[27m
2025-08-30T08:19:55 [7m>>> host-util-linux 2.39.3 Configuring[27m
2025-08-30T08:19:55 [7m>>> host-util-linux 2.39.3 Autoreconfiguring[27m
2025-08-30T08:20:00 [7m>>> host-util-linux 2.39.3 Patching libtool[27m
2025-08-30T08:20:09 [7m>>> host-util-linux 2.39.3 Building[27m
2025-08-30T08:20:17 [7m>>> host-util-linux 2.39.3 Installing to host directory[27m
2025-08-30T08:20:18 [7m>>> host-e2fsprogs 1.47.0 Extracting[27m
2025-08-30T08:20:19 [7m>>> host-e2fsprogs 1.47.0 Patching[27m
2025-08-30T08:20:19 [7m>>> host-e2fsprogs 1.47.0 Updating config.sub and config.guess[27m
2025-08-30T08:20:19 [7m>>> host-e2fsprogs 1.47.0 Patching libtool[27m
2025-08-30T08:20:19 [7m>>> host-e2fsprogs 1.47.0 Configuring[27m
2025-08-30T08:20:25 [7m>>> host-e2fsprogs 1.47.0 Building[27m
2025-08-30T08:20:52 [7m>>> host-e2fsprogs 1.47.0 Installing to host directory[27m
2025-08-30T08:20:54 [7m>>> host-environment-setup  Extracting[27m
2025-08-30T08:20:54 [7m>>> host-environment-setup  Patching[27m
2025-08-30T08:20:54 [7m>>> host-environment-setup  Configuring[27m
2025-08-30T08:20:54 [7m>>> host-environment-setup  Building[27m
2025-08-30T08:20:54 [7m>>> host-environment-setup  Installing to host directory[27m
2025-08-30T08:20:55 [7m>>> parted 3.6 Downloading[27m
2025-08-30T08:20:57 [7m>>> parted 3.6 Extracting[27m
2025-08-30T08:20:57 [7m>>> parted 3.6 Patching[27m
2025-08-30T08:20:57 [7m>>> parted 3.6 Updating config.sub and config.guess[27m
2025-08-30T08:20:57 [7m>>> parted 3.6 Patching libtool[27m
2025-08-30T08:20:57 [7m>>> parted 3.6 Configuring[27m
2025-08-30T08:21:09 [7m>>> parted 3.6 Building[27m
2025-08-30T08:21:13 [7m>>> parted 3.6 Installing to staging directory[27m
2025-08-30T08:21:14 [7m>>> parted 3.6 Fixing libtool files[27m
2025-08-30T08:21:14 [7m>>> parted 3.6 Installing to target[27m
2025-08-30T08:21:14 [7m>>> fatresize 321973ba156bbf2489e82c47c94b2bca74b16316 Downloading[27m
2025-08-30T08:21:14 [7m>>> fatresize 321973ba156bbf2489e82c47c94b2bca74b16316 Extracting[27m
2025-08-30T08:21:14 [7m>>> fatresize 321973ba156bbf2489e82c47c94b2bca74b16316 Patching[27m
2025-08-30T08:21:14 [7m>>> fatresize 321973ba156bbf2489e82c47c94b2bca74b16316 Updating config.sub and config.guess[27m
2025-08-30T08:21:14 [7m>>> fatresize 321973ba156bbf2489e82c47c94b2bca74b16316 Configuring[27m
2025-08-30T08:21:15 [7m>>> fatresize 321973ba156bbf2489e82c47c94b2bca74b16316 Autoreconfiguring[27m
2025-08-30T08:21:16 [7m>>> fatresize 321973ba156bbf2489e82c47c94b2bca74b16316 Patching libtool[27m
2025-08-30T08:21:18 [7m>>> fatresize 321973ba156bbf2489e82c47c94b2bca74b16316 Building[27m
2025-08-30T08:21:18 [7m>>> fatresize 321973ba156bbf2489e82c47c94b2bca74b16316 Installing to target[27m
2025-08-30T08:21:18 [7m>>> ifupdown-scripts  Extracting[27m
2025-08-30T08:21:18 [7m>>> ifupdown-scripts  Patching[27m
2025-08-30T08:21:18 [7m>>> ifupdown-scripts  Configuring[27m
2025-08-30T08:21:18 [7m>>> ifupdown-scripts  Building[27m
2025-08-30T08:21:18 [7m>>> ifupdown-scripts  Installing to target[27m
2025-08-30T08:21:18 [7m>>> initscripts  Extracting[27m
2025-08-30T08:21:19 [7m>>> initscripts  Patching[27m
2025-08-30T08:21:19 [7m>>> initscripts  Configuring[27m
2025-08-30T08:21:19 [7m>>> initscripts  Building[27m
2025-08-30T08:21:19 [7m>>> initscripts  Installing to target[27m
2025-08-30T08:21:19 [7m>>> libcurl 8.6.0 Downloading[27m
2025-08-30T08:21:21 [7m>>> libcurl 8.6.0 Extracting[27m
2025-08-30T08:21:21 [7m>>> libcurl 8.6.0 Patching[27m
2025-08-30T08:21:21 [7m>>> libcurl 8.6.0 Updating config.sub and config.guess[27m
2025-08-30T08:21:21 [7m>>> libcurl 8.6.0 Configuring[27m
2025-08-30T08:21:21 [7m>>> libcurl 8.6.0 Autoreconfiguring[27m
2025-08-30T08:21:28 [7m>>> libcurl 8.6.0 Patching libtool[27m
2025-08-30T08:21:37 [7m>>> libcurl 8.6.0 Building[27m
2025-08-30T08:21:42 [7m>>> libcurl 8.6.0 Installing to staging directory[27m
2025-08-30T08:21:42 [7m>>> libcurl 8.6.0 Fixing package configuration files[27m
2025-08-30T08:21:42 [7m>>> libcurl 8.6.0 Fixing libtool files[27m
2025-08-30T08:21:42 [7m>>> libcurl 8.6.0 Installing to target[27m
2025-08-30T08:21:43 [7m>>> host-cmake 3.28.1 Extracting[27m
2025-08-30T08:21:44 [7m>>> host-cmake 3.28.1 Patching[27m
2025-08-30T08:21:44 [7m>>> host-cmake 3.28.1 Configuring[27m
2025-08-30T08:22:50 [7m>>> host-cmake 3.28.1 Building[27m
2025-08-30T08:24:58 [7m>>> host-cmake 3.28.1 Installing to host directory[27m
2025-08-30T08:24:59 [7m>>> host-ninja 1.11.1.g95dee.kitware.jobserver-1 Extracting[27m
2025-08-30T08:24:59 [7m>>> host-ninja 1.11.1.g95dee.kitware.jobserver-1 Patching[27m
2025-08-30T08:24:59 [7m>>> host-ninja 1.11.1.g95dee.kitware.jobserver-1 Configuring[27m
2025-08-30T08:25:00 [7m>>> host-ninja 1.11.1.g95dee.kitware.jobserver-1 Building[27m
2025-08-30T08:25:03 [7m>>> host-ninja 1.11.1.g95dee.kitware.jobserver-1 Installing to host directory[27m
2025-08-30T08:25:04 [7m>>> host-python-flit-core 3.9.0 Extracting[27m
2025-08-30T08:25:04 [7m>>> host-python-flit-core 3.9.0 Patching[27m
2025-08-30T08:25:04 [7m>>> host-python-flit-core 3.9.0 Configuring[27m
2025-08-30T08:25:04 [7m>>> host-python-flit-core 3.9.0 Building[27m
2025-08-30T08:25:05 [7m>>> host-python-flit-core 3.9.0 Installing to host directory[27m
2025-08-30T08:25:05 [7m>>> host-python-installer 0.7.0 Extracting[27m
2025-08-30T08:25:06 [7m>>> host-python-installer 0.7.0 Patching[27m
2025-08-30T08:25:06 [7m>>> host-python-installer 0.7.0 Configuring[27m
2025-08-30T08:25:06 [7m>>> host-python-installer 0.7.0 Building[27m
2025-08-30T08:25:06 [7m>>> host-python-installer 0.7.0 Installing to host directory[27m
2025-08-30T08:25:07 [7m>>> host-python-packaging 23.2 Extracting[27m
2025-08-30T08:25:07 [7m>>> host-python-packaging 23.2 Patching[27m
2025-08-30T08:25:07 [7m>>> host-python-packaging 23.2 Configuring[27m
2025-08-30T08:25:07 [7m>>> host-python-packaging 23.2 Building[27m
2025-08-30T08:25:07 [7m>>> host-python-packaging 23.2 Installing to host directory[27m
2025-08-30T08:25:08 [7m>>> host-python-pyproject-hooks 1.0.0 Extracting[27m
2025-08-30T08:25:08 [7m>>> host-python-pyproject-hooks 1.0.0 Patching[27m
2025-08-30T08:25:08 [7m>>> host-python-pyproject-hooks 1.0.0 Configuring[27m
2025-08-30T08:25:08 [7m>>> host-python-pyproject-hooks 1.0.0 Building[27m
2025-08-30T08:25:08 [7m>>> host-python-pyproject-hooks 1.0.0 Installing to host directory[27m
2025-08-30T08:25:09 [7m>>> host-python-pypa-build 1.0.3 Extracting[27m
2025-08-30T08:25:09 [7m>>> host-python-pypa-build 1.0.3 Patching[27m
2025-08-30T08:25:09 [7m>>> host-python-pypa-build 1.0.3 Configuring[27m
2025-08-30T08:25:09 [7m>>> host-python-pypa-build 1.0.3 Building[27m
2025-08-30T08:25:09 [7m>>> host-python-pypa-build 1.0.3 Installing to host directory[27m
2025-08-30T08:25:10 [7m>>> host-python-wheel 0.40.0 Extracting[27m
2025-08-30T08:25:10 [7m>>> host-python-wheel 0.40.0 Patching[27m
2025-08-30T08:25:10 [7m>>> host-python-wheel 0.40.0 Configuring[27m
2025-08-30T08:25:10 [7m>>> host-python-wheel 0.40.0 Building[27m
2025-08-30T08:25:11 [7m>>> host-python-wheel 0.40.0 Installing to host directory[27m
2025-08-30T08:25:12 [7m>>> host-python-setuptools 69.0.3 Extracting[27m
2025-08-30T08:25:12 [7m>>> host-python-setuptools 69.0.3 Patching[27m
2025-08-30T08:25:12 [7m>>> host-python-setuptools 69.0.3 Configuring[27m
2025-08-30T08:25:12 [7m>>> host-python-setuptools 69.0.3 Building[27m
2025-08-30T08:25:13 [7m>>> host-python-setuptools 69.0.3 Installing to host directory[27m
2025-08-30T08:25:14 [7m>>> host-meson 1.3.1 Extracting[27m
2025-08-30T08:25:14 [7m>>> host-meson 1.3.1 Patching[27m
2025-08-30T08:25:14 [7m>>> host-meson 1.3.1 Configuring[27m
2025-08-30T08:25:14 [7m>>> host-meson 1.3.1 Building[27m
2025-08-30T08:25:15 [7m>>> host-meson 1.3.1 Installing to host directory[27m
2025-08-30T08:25:17 [7m>>> libpthread-stubs 0.5 Extracting[27m
2025-08-30T08:25:17 [7m>>> libpthread-stubs 0.5 Patching[27m
2025-08-30T08:25:17 [7m>>> libpthread-stubs 0.5 Updating config.sub and config.guess[27m
2025-08-30T08:25:17 [7m>>> libpthread-stubs 0.5 Patching libtool[27m
2025-08-30T08:25:17 [7m>>> libpthread-stubs 0.5 Configuring[27m
2025-08-30T08:25:18 [7m>>> libpthread-stubs 0.5 Building[27m
2025-08-30T08:25:19 [7m>>> libpthread-stubs 0.5 Installing to staging directory[27m
2025-08-30T08:25:19 [7m>>> libpthread-stubs 0.5 Fixing libtool files[27m
2025-08-30T08:25:19 [7m>>> libpthread-stubs 0.5 Installing to target[27m
2025-08-30T08:25:19 [7m>>> libdrm 2.4.124 Extracting[27m
2025-08-30T08:25:19 [7m>>> libdrm 2.4.124 Patching[27m
2025-08-30T08:25:19 [7m>>> libdrm 2.4.124 Configuring[27m
2025-08-30T08:25:21 [7m>>> libdrm 2.4.124 Building[27m
2025-08-30T08:25:22 [7m>>> libdrm 2.4.124 Installing to staging directory[27m
2025-08-30T08:25:22 [7m>>> libdrm 2.4.124 Fixing libtool files[27m
2025-08-30T08:25:22 [7m>>> libdrm 2.4.124 Installing to target[27m
2025-08-30T08:25:22 [7m>>> libpng 1.6.42 Downloading[27m
2025-08-30T08:25:24 [7m>>> libpng 1.6.42 Extracting[27m
2025-08-30T08:25:24 [7m>>> libpng 1.6.42 Patching[27m
2025-08-30T08:25:24 [7m>>> libpng 1.6.42 Updating config.sub and config.guess[27m
2025-08-30T08:25:24 [7m>>> libpng 1.6.42 Patching libtool[27m
2025-08-30T08:25:24 [7m>>> libpng 1.6.42 Configuring[27m
2025-08-30T08:25:26 [7m>>> libpng 1.6.42 Building[27m
2025-08-30T08:25:29 [7m>>> libpng 1.6.42 Installing to staging directory[27m
2025-08-30T08:25:29 [7m>>> libpng 1.6.42 Fixing package configuration files[27m
2025-08-30T08:25:29 [7m>>> libpng 1.6.42 Fixing libtool files[27m
2025-08-30T08:25:29 [7m>>> libpng 1.6.42 Installing to target[27m
2025-08-30T08:25:29 [7m>>> host-ntfs-3g 2022.10.3 Extracting[27m
2025-08-30T08:25:29 [7m>>> host-ntfs-3g 2022.10.3 Patching[27m
2025-08-30T08:25:29 [7m>>> host-ntfs-3g 2022.10.3 Updating config.sub and config.guess[27m
2025-08-30T08:25:29 [7m>>> host-ntfs-3g 2022.10.3 Patching libtool[27m
2025-08-30T08:25:29 [7m>>> host-ntfs-3g 2022.10.3 Configuring[27m
2025-08-30T08:25:34 [7m>>> host-ntfs-3g 2022.10.3 Building[27m
2025-08-30T08:25:38 [7m>>> host-ntfs-3g 2022.10.3 Installing to host directory[27m
2025-08-30T08:25:39 [7m>>> host-patchelf 0.13 Extracting[27m
2025-08-30T08:25:39 [7m>>> host-patchelf 0.13 Patching[27m
2025-08-30T08:25:39 [7m>>> host-patchelf 0.13 Updating config.sub and config.guess[27m
2025-08-30T08:25:39 [7m>>> host-patchelf 0.13 Patching libtool[27m
2025-08-30T08:25:39 [7m>>> host-patchelf 0.13 Configuring[27m
2025-08-30T08:25:40 [7m>>> host-patchelf 0.13 Building[27m
2025-08-30T08:25:43 [7m>>> host-patchelf 0.13 Installing to host directory[27m
2025-08-30T08:25:44 [7m>>> pm-utils 1.4.1 Extracting[27m
2025-08-30T08:25:44 [7m>>> pm-utils 1.4.1 Patching[27m
2025-08-30T08:25:44 [7m>>> pm-utils 1.4.1 Updating config.sub and config.guess[27m
2025-08-30T08:25:44 [7m>>> pm-utils 1.4.1 Patching libtool[27m
2025-08-30T08:25:44 [7m>>> pm-utils 1.4.1 Configuring[27m
2025-08-30T08:25:46 [7m>>> pm-utils 1.4.1 Building[27m
2025-08-30T08:25:46 [7m>>> pm-utils 1.4.1 Installing to target[27m
2025-08-30T08:25:46 [7m>>> recovery develop Syncing from source dir /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/../external/recovery[27m
2025-08-30T08:25:46 [7m>>> recovery develop Configuring[27m
2025-08-30T08:25:46 [7m>>> recovery develop Building[27m
2025-08-30T08:25:47 [7m>>> recovery develop Installing to target[27m
2025-08-30T08:25:47 [7m>>> rkscript  Extracting[27m
2025-08-30T08:25:47 [7m>>> rkscript  Patching[27m
2025-08-30T08:25:47 [7m>>> rkscript  Configuring[27m
2025-08-30T08:25:47 [7m>>> rkscript  Building[27m
2025-08-30T08:25:47 [7m>>> rkscript  Installing to target[27m
2025-08-30T08:25:47 [7m>>> rktoolkit master Syncing from source dir /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/../external/rktoolkit[27m
2025-08-30T08:25:47 [7m>>> rktoolkit master Configuring[27m
2025-08-30T08:25:48 [7m>>> rktoolkit master Building[27m
2025-08-30T08:25:48 [7m>>> rktoolkit master Installing to target[27m
2025-08-30T08:25:48 [7m>>> rkupdate develop Syncing from source dir /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/../external/rkupdate[27m
2025-08-30T08:25:48 [7m>>> rkupdate develop Configuring[27m
2025-08-30T08:25:49 [7m>>> rkupdate develop Building[27m
2025-08-30T08:25:50 [7m>>> rkupdate develop Installing to target[27m
2025-08-30T08:25:51 [7m>>> urandom-scripts  Extracting[27m
2025-08-30T08:25:51 [7m>>> urandom-scripts  Patching[27m
2025-08-30T08:25:51 [7m>>> urandom-scripts  Configuring[27m
2025-08-30T08:25:51 [7m>>> urandom-scripts  Building[27m
2025-08-30T08:25:51 [7m>>> urandom-scripts  Installing to target[27m
2025-08-30T08:25:51 [7m>>> usbmount 0.0.22 Extracting[27m
2025-08-30T08:25:51 [7m>>> usbmount 0.0.22 Patching[27m
2025-08-30T08:25:51 [7m>>> usbmount 0.0.22 Configuring[27m
2025-08-30T08:25:51 [7m>>> usbmount 0.0.22 Building[27m
2025-08-30T08:25:51 [7m>>> usbmount 0.0.22 Installing to target[27m
2025-08-30T08:25:51 [7m>>> host-lz4 1.9.4 Extracting[27m
2025-08-30T08:25:52 [7m>>> host-lz4 1.9.4 Patching[27m
2025-08-30T08:25:52 [7m>>> host-lz4 1.9.4 Configuring[27m
2025-08-30T08:25:52 [7m>>> host-lz4 1.9.4 Building[27m
2025-08-30T08:25:58 [7m>>> host-lz4 1.9.4 Installing to host directory[27m
2025-08-30T08:25:59 [7m>>> host-lzo 2.10 Extracting[27m
2025-08-30T08:25:59 [7m>>> host-lzo 2.10 Patching[27m
2025-08-30T08:25:59 [7m>>> host-lzo 2.10 Configuring[27m
2025-08-30T08:26:04 [7m>>> host-lzo 2.10 Building[27m
2025-08-30T08:26:05 [7m>>> host-lzo 2.10 Installing to host directory[27m
2025-08-30T08:26:06 [7m>>> host-zstd 1.5.5 Extracting[27m
2025-08-30T08:26:06 [7m>>> host-zstd 1.5.5 Patching[27m
2025-08-30T08:26:06 [7m>>> host-zstd 1.5.5 Configuring[27m
2025-08-30T08:26:06 [7m>>> host-zstd 1.5.5 Building[27m
2025-08-30T08:26:15 [7m>>> host-zstd 1.5.5 Installing to host directory[27m
2025-08-30T08:26:17 [7m>>> host-squashfs 4.6.1 Extracting[27m
2025-08-30T08:26:17 [7m>>> host-squashfs 4.6.1 Patching[27m
2025-08-30T08:26:17 [7m>>> host-squashfs 4.6.1 Configuring[27m
2025-08-30T08:26:17 [7m>>> host-squashfs 4.6.1 Building[27m
2025-08-30T08:26:19 [7m>>> host-squashfs 4.6.1 Installing to host directory[27m
2025-08-30T08:26:20 [7m>>>   Finalizing host directory[27m
2025-08-30T08:26:20 [7m>>>   Finalizing target directory[27m
2025-08-30T08:26:21 [7m>>>   Sanitizing RPATH in target tree[27m
2025-08-30T08:26:21 [7m>>>   Sanity check in overlay board/rockchip/common/base[27m
2025-08-30T08:26:21 [7m>>>   Sanity check in overlay board/rockchip/rv1126b/fs-overlay/[27m
2025-08-30T08:26:21 [7m>>>   Copying overlay board/rockchip/common/base[27m
2025-08-30T08:26:21 [7m>>>   Copying overlay board/rockchip/rv1126b/fs-overlay/[27m
2025-08-30T08:26:21 [7m>>>   Executing post-build script board/rockchip/common/post-build.sh[27m
2025-08-30T08:26:21 >>> Ignored board/rockchip/common/overlays/10-weston
2025-08-30T08:26:21 >>> Ignored board/rockchip/common/overlays/11-weston-chromium
2025-08-30T08:26:21 >>> Ignored board/rockchip/common/overlays/20-wlan0
2025-08-30T08:26:21 >>> Copying board/rockchip/common/overlays/default
2025-08-30T08:26:21 >>> [35mRunning 01-hostname.sh for /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/target (recovery init=busybox)...[0m
2025-08-30T08:26:21 >>> [35mRunning 10-os-release.sh for /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/target (recovery init=busybox)...[0m
2025-08-30T08:26:21 >>> [35mRunning 30-fstab.sh for /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/target (recovery init=busybox)...[0m
2025-08-30T08:26:21 >>> [35mRunning 40-busybox-reboot.sh for /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/target (recovery init=busybox)...[0m
2025-08-30T08:26:22 >>> [35mRunning 50-locale.sh for /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/target (recovery init=busybox)...[0m
2025-08-30T08:26:22 >>> [35mRunning 90-overlay.sh for /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/target (recovery init=busybox)...[0m
2025-08-30T08:26:22 >>> [35mHandling overlay: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/overlays/common/default)...[0m
2025-08-30T08:26:22 >>> [35mInstalling overlay: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/overlays/common/udev-rules to /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/target...[0m
2025-08-30T08:26:22 >>> [35mHandling overlay: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/overlays/recovery/adbd)...[0m
2025-08-30T08:26:22 >>> [35mHandling overlay: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/overlays/recovery/default)...[0m
2025-08-30T08:26:22 >>> [35mRunning 91-modules.sh for /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/target (recovery init=busybox)...[0m
2025-08-30T08:26:22 >>> [35mRunning 97-ldcache.sh for /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/target (recovery init=busybox)...[0m
2025-08-30T08:26:22 >>> [35mRunning 99-owner.sh for /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/target (recovery init=busybox)...[0m
2025-08-30T08:26:22 >>> [35mRunning build.sh - post-rootfs succeeded.[0m
2025-08-30T08:26:22 [7m>>>   Generating root filesystems common tables[27m
2025-08-30T08:26:22 [7m>>>   Generating filesystem image rootfs.cpio[27m
2025-08-30T08:26:23 [7m>>>   Generating filesystem image rootfs.ext2[27m
2025-08-30T08:26:24 [7m>>>   Generating filesystem image rootfs.squashfs[27m
2025-08-30T08:26:24 [7m>>>   Generating filesystem image rootfs.tar[27m
Done in 23min 44s
[35mLog saved on /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/sessions/2025-08-30_08-00-14/br-rockchip_rv1126b_recovery_2025-08-30_08-02-41.log[0m
[35mGenerated images:[0m
/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/recovery/images/rootfs.cpio
/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/recovery/images/rootfs.cpio.gz
/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/recovery/images/rootfs.ext2
/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/recovery/images/rootfs.ext4
/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/recovery/images/rootfs.squashfs
/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/recovery/images/rootfs.tar
[35mUsing kernel version(6.1) from environment[0m
[36mToolchain for kernel:[0m
[36m/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/prebuilts/gcc/linux-x86/aarch64/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-[0m

[36m==========================================[0m
[36m          Start building recovery-kernel[0m
[36m==========================================[0m
[35m+ rm -rf /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/recovery-kernel[0m
[35m+ ln -rsf /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/kernel /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/recovery-kernel[0m
[35m+ cd /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/recovery-kernel[0m
[36m# Found kernel's basic config fragment: rv1126b.config[0m
[35m+ make -C /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/kernel/ -j23 CROSS_COMPILE=/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/prebuilts/gcc/linux-x86/aarch64/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu- ARCH=arm64 rv1126b_defconfig rv1126b.config[0m
make: Entering directory '/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/kernel-6.1'
#
# configuration written to .config
#
Using .config as base
Merging ./arch/arm64/configs/rv1126b.config
Value of CONFIG_ROCKCHIP_RKNPU_PROC_FS is redefined by fragment ./arch/arm64/configs/rv1126b.config:
Previous value: # CONFIG_ROCKCHIP_RKNPU_PROC_FS is not set
New value: CONFIG_ROCKCHIP_RKNPU_PROC_FS=y

Value of CONFIG_ROCKCHIP_RKNPU_DMA_HEAP is redefined by fragment ./arch/arm64/configs/rv1126b.config:
Previous value: # CONFIG_ROCKCHIP_RKNPU_DMA_HEAP is not set
New value: CONFIG_ROCKCHIP_RKNPU_DMA_HEAP=y

#
# merged configuration written to .config (needs make)
#
.config:5820:warning: override: ROCKCHIP_RKNPU_DMA_HEAP changes choice state
#
# configuration written to .config
#
make: Leaving directory '/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/kernel-6.1'
[35m+ make -C /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/kernel/ -j23 CROSS_COMPILE=/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/prebuilts/gcc/linux-x86/aarch64/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu- ARCH=arm64 Image[0m
make: Entering directory '/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/kernel-6.1'
  SYNC    include/config/auto.conf.cmd
  CALL    scripts/checksyscalls.sh
  MODPOST vmlinux.symvers
make: Leaving directory '/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/kernel-6.1'
[35m+ make -C /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/kernel/ -j23 CROSS_COMPILE=/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/prebuilts/gcc/linux-x86/aarch64/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu- ARCH=arm64 rockchip/rv1126bp-evb-v14.dtb[0m
make: Entering directory '/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/kernel-6.1'
make: Leaving directory '/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/kernel-6.1'
[35m+ ln -rsf arch/arm64/boot/Image /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/recovery-kernel.img[0m
[35m+ ln -rsf arch/arm64/boot/dts/rockchip/rv1126bp-evb-v14.dtb /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/recovery-kernel.dtb[0m
[35m+ scripts/resource_tool arch/arm64/boot/dts/rockchip/rv1126bp-evb-v14.dtb logo.bmp logo_kernel.bmp[0m
Pack to resource.img successed!
[35m+ ln -rsf resource.img /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/recovery-resource.img[0m
[35mRunning mk-kernel.sh - build_recovery-kernel succeeded.[0m
Packing /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/recovery/ramboot.img...
FIT description: U-Boot FIT source file for arm
Created:         Sat Aug 30 08:26:30 2025
 Image 0 (fdt)
  Description:  unavailable
  Created:      Sat Aug 30 08:26:30 2025
  Type:         Flat Device Tree
  Compression:  uncompressed
  Data Size:    68490 Bytes = 66.88 KiB = 0.07 MiB
  Architecture: AArch64
  Load Address: 0xffffff00
  Hash algo:    sha256
  Hash value:   1c05afdd29fb6e150db47a6b11abacaa978ceda0bc748ae9806164800c1c8046
 Image 1 (kernel)
  Description:  unavailable
  Created:      Sat Aug 30 08:26:30 2025
  Type:         Kernel Image
  Compression:  uncompressed
  Data Size:    13174792 Bytes = 12866.01 KiB = 12.56 MiB
  Architecture: AArch64
  OS:           Linux
  Load Address: 0xffffff01
  Entry Point:  0xffffff01
  Hash algo:    sha256
  Hash value:   055cd25c789f3177debc87969b445affdf43e6c4e9c24eb484e01384f8e24d3d
 Image 2 (ramdisk)
  Description:  unavailable
  Created:      Sat Aug 30 08:26:30 2025
  Type:         RAMDisk Image
  Compression:  uncompressed
  Data Size:    5646368 Bytes = 5514.03 KiB = 5.38 MiB
  Architecture: AArch64
  OS:           Linux
  Load Address: 0xffffff02
  Entry Point:  unavailable
  Hash algo:    sha256
  Hash value:   48e97c88b7cdf4cf8ea997547cd2d8c2b8c226de8baa39a53e6d89a1f4f347c7
 Image 3 (resource)
  Description:  unavailable
  Created:      Sat Aug 30 08:26:30 2025
  Type:         Multi-File Image
  Compression:  uncompressed
  Data Size:    106496 Bytes = 104.00 KiB = 0.10 MiB
  Hash algo:    sha256
  Hash value:   30d8be602c2723199411a361c4689606b2077c737053dad3f84fb8d8a696e9d4
 Default Configuration: 'conf'
 Configuration 0 (conf)
  Description:  unavailable
  Kernel:       kernel
  Init Ramdisk: ramdisk
  FDT:          fdt
[35mRunning mk-recovery.sh - build_recovery succeeded.[0m
[36m==========================================[0m
[36m          Start packing firmwares[0m
[36m==========================================[0m
[36mLinking /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/firmware/parameter.txt from /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/.chip/parameter.txt...[0m
[35mPreparing partiton oem[0m
[36mMerging /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/extra-parts/oem/normal into /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/extra-parts/oem[0m
[35mPacking /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/extra-parts/oem.img from /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/extra-parts/oem.fs[0m
Using host tools in /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/latest/host (except for mke2fs)
Making /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/extra-parts/oem.img from /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/extra-parts/oem (auto sized)
Making /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/extra-parts/oem.img from /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/extra-parts/oem with size(13656KB)
/sbin/mke2fs: symbol lookup error: /sbin/mke2fs: undefined symbol: test_io_manager
Retring with increased size....(1/20)
Making /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/extra-parts/oem.img from /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/extra-parts/oem with size(17752KB)
/sbin/mke2fs: symbol lookup error: /sbin/mke2fs: undefined symbol: test_io_manager
Retring with increased size....(2/20)
Making /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/extra-parts/oem.img from /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/extra-parts/oem with size(21848KB)
/sbin/mke2fs: symbol lookup error: /sbin/mke2fs: undefined symbol: test_io_manager
Retring with increased size....(3/20)
Making /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/extra-parts/oem.img from /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/extra-parts/oem with size(25944KB)
/sbin/mke2fs: symbol lookup error: /sbin/mke2fs: undefined symbol: test_io_manager
Retring with increased size....(4/20)
Making /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/extra-parts/oem.img from /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/extra-parts/oem with size(30040KB)
/sbin/mke2fs: symbol lookup error: /sbin/mke2fs: undefined symbol: test_io_manager
Retring with increased size....(5/20)
Making /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/extra-parts/oem.img from /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/extra-parts/oem with size(34136KB)
/sbin/mke2fs: symbol lookup error: /sbin/mke2fs: undefined symbol: test_io_manager
Retring with increased size....(6/20)
Making /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/extra-parts/oem.img from /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/extra-parts/oem with size(38232KB)
/sbin/mke2fs: symbol lookup error: /sbin/mke2fs: undefined symbol: test_io_manager
Retring with increased size....(7/20)
Making /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/extra-parts/oem.img from /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/extra-parts/oem with size(42328KB)
/sbin/mke2fs: symbol lookup error: /sbin/mke2fs: undefined symbol: test_io_manager
Retring with increased size....(8/20)
Making /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/extra-parts/oem.img from /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/extra-parts/oem with size(46424KB)
/sbin/mke2fs: symbol lookup error: /sbin/mke2fs: undefined symbol: test_io_manager
Retring with increased size....(9/20)
Making /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/extra-parts/oem.img from /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/extra-parts/oem with size(50520KB)
/sbin/mke2fs: symbol lookup error: /sbin/mke2fs: undefined symbol: test_io_manager
Retring with increased size....(10/20)
Making /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/extra-parts/oem.img from /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/extra-parts/oem with size(54616KB)
/sbin/mke2fs: symbol lookup error: /sbin/mke2fs: undefined symbol: test_io_manager
Retring with increased size....(11/20)
Making /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/extra-parts/oem.img from /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/extra-parts/oem with size(58712KB)
/sbin/mke2fs: symbol lookup error: /sbin/mke2fs: undefined symbol: test_io_manager
Retring with increased size....(12/20)
Making /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/extra-parts/oem.img from /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/extra-parts/oem with size(62808KB)
/sbin/mke2fs: symbol lookup error: /sbin/mke2fs: undefined symbol: test_io_manager
Retring with increased size....(13/20)
Making /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/extra-parts/oem.img from /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/extra-parts/oem with size(66904KB)
/sbin/mke2fs: symbol lookup error: /sbin/mke2fs: undefined symbol: test_io_manager
Retring with increased size....(14/20)
Making /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/extra-parts/oem.img from /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/extra-parts/oem with size(71000KB)
/sbin/mke2fs: symbol lookup error: /sbin/mke2fs: undefined symbol: test_io_manager
Retring with increased size....(15/20)
Making /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/extra-parts/oem.img from /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/extra-parts/oem with size(75096KB)
/sbin/mke2fs: symbol lookup error: /sbin/mke2fs: undefined symbol: test_io_manager
Retring with increased size....(16/20)
Making /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/extra-parts/oem.img from /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/extra-parts/oem with size(79192KB)
/sbin/mke2fs: symbol lookup error: /sbin/mke2fs: undefined symbol: test_io_manager
Retring with increased size....(17/20)
Making /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/extra-parts/oem.img from /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/extra-parts/oem with size(83288KB)
/sbin/mke2fs: symbol lookup error: /sbin/mke2fs: undefined symbol: test_io_manager
Retring with increased size....(18/20)
Making /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/extra-parts/oem.img from /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/extra-parts/oem with size(87384KB)
/sbin/mke2fs: symbol lookup error: /sbin/mke2fs: undefined symbol: test_io_manager
Retring with increased size....(19/20)
Making /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/extra-parts/oem.img from /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/extra-parts/oem with size(91480KB)
/sbin/mke2fs: symbol lookup error: /sbin/mke2fs: undefined symbol: test_io_manager
Retring with increased size....(20/20)
Making /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/extra-parts/oem.img from /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/extra-parts/oem with size(95576KB)
/sbin/mke2fs: symbol lookup error: /sbin/mke2fs: undefined symbol: test_io_manager
FATAL:  Failed to make image!
[31mERROR: Running /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/scripts/mk-extra-parts.sh - post_build_hook failed![0m
[31mERROR: exit code 1 from line 74:[0m
[31m    fakeroot -- "$FAKEROOT_SCRIPT"[0m
[31mERROR: call stack:[0m
[31m    mk-extra-parts.sh: post_build_hook(74)[0m
[31m    mk-extra-parts.sh: main(89)[0m
[31mERROR: Running /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/scripts/mk-firmware.sh - build_firmware failed![0m
[31mERROR: exit code 1 from line 22:[0m
[31m    "$RK_SCRIPTS_DIR/mk-extra-parts.sh"[0m
[31mERROR: call stack:[0m
[31m    mk-firmware.sh: build_firmware(22)[0m
[31m    mk-firmware.sh: post_build_hook(93)[0m
[31m    mk-firmware.sh: main(98)[0m
[31mERROR: Running /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/build-hooks/99-all.sh - build_all failed![0m
[31mERROR: exit code 1 from line 28:[0m
[31m    "$RK_SCRIPTS_DIR/mk-firmware.sh"[0m
[31mERROR: call stack:[0m
[31m    99-all.sh: build_all(28)[0m
[31m    99-all.sh: build_hook(136)[0m
[31m    build-helper: try_func(63)[0m
[31m    build-helper: try_hook(96)[0m
[31m    build-helper: source(165)[0m
[31m    99-all.sh: main(147)[0m

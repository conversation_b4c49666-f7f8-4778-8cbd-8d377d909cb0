# 2025-08-30 08:51:14
# run hook: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/target

[35mRunning 01-hostname.sh for /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/target (recovery init=busybox)...[0m
[36mSetting hostname: rv1126bp-recovery[0m
[35mRunning 10-os-release.sh for /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/target (recovery init=busybox)...[0m
[36mAdding information to /etc/os-release...[0m
[35mRunning 30-fstab.sh for /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/target (recovery init=busybox)...[0m
[36mFixing up /etc/fstab...[0m
[36mFixing up rootfs type: ext4[0m
[36mFixup basic partitions for non-systemd init...[0m
[36mFixing up basic partition: proc /proc[0m
[36mFixing up basic partition: devtmpfs /dev[0m
[36mFixing up basic partition: devpts /dev/pts mode=0620,ptmxmode=0000,gid=5[0m
[36mFixing up basic partition: tmpfs /dev/shm nosuid,nodev,noexec[0m
[36mFixing up basic partition: sysfs /sys nosuid,nodev,noexec[0m
[36mFixing up basic partition: configfs /sys/kernel/config[0m
[36mFixing up basic partition: debugfs /sys/kernel/debug[0m
[36mFixing up basic partition: pstore /sys/fs/pstore nosuid,nodev,noexec[0m
[36mFixing up device partition: /dev/sda1 /mnt/udisk auto[0m
[36mFixing up device partition: /dev/mmcblk1p1 /mnt/sdcard auto[0m
[36mFixing up device partition: PARTLABEL=oem /oem ext4 defaults[0m
[36mFixing up device partition: PARTLABEL=userdata /userdata ext4 defaults[0m
[35mRunning 40-busybox-reboot.sh for /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/target (recovery init=busybox)...[0m
[35mRunning 50-locale.sh for /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/target (recovery init=busybox)...[0m
[36mSetting LANG environment to en_US.UTF-8...[0m
[35mRunning 90-overlay.sh for /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/target (recovery init=busybox)...[0m
[35mHandling overlay: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/overlays/common/default)...[0m
sending incremental file list
./

sent 53 bytes  received 19 bytes  144.00 bytes/sec
total size is 0  speedup is 0.00
[35mInstalling overlay: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/overlays/common/udev-rules to /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/target...[0m
sending incremental file list
lib/
lib/udev/

sent 226 bytes  received 21 bytes  494.00 bytes/sec
total size is 1,854  speedup is 7.51
[35mHandling overlay: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/overlays/recovery/adbd)...[0m
[35mADBD disabled...[0m
[35mHandling overlay: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/overlays/recovery/default)...[0m
sending incremental file list

sent 46 bytes  received 12 bytes  116.00 bytes/sec
total size is 0  speedup is 0.00
[35mRunning 91-modules.sh for /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/target (recovery init=busybox)...[0m
[36mStrip kernel modules...[0m
[35mRunning 97-ldcache.sh for /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/target (recovery init=busybox)...[0m
[35mRunning 99-owner.sh for /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/target (recovery init=busybox)...[0m
[36mFixing up owner for /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output...[0m
changed ownership of '/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/firmware/rootfs.img' from root:root to 1000:1000
changed ownership of '/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/recovery/images' from root:root to 1000:1000
changed ownership of '/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/buildroot/target' from root:root to 1000:1000
changed ownership of '/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/buildroot/.files_post.txt' from root:root to 1000:1000
changed ownership of '/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/buildroot/.stamp_build_finish' from root:root to 1000:1000
changed ownership of '/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/sessions/2025-08-30_08-50-17/post-rootfs.log' from root:root to 1000:1000
changed ownership of '/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/sessions/2025-08-30_08-50-17/post-rootfs_2025-08-30_08-51-14.log' from root:root to 1000:1000
changed ownership of '/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/sessions/2025-08-30_08-50-17/br.log' from root:root to 1000:1000
changed ownership of '/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/sessions/2025-08-30_08-50-17/br-rockchip_rv1126b_recovery.log' from root:root to 1000:1000
changed ownership of '/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/sessions/2025-08-30_08-50-17/br-rockchip_rv1126b_recovery_2025-08-30_08-51-08.log' from root:root to 1000:1000
changed ownership of '/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/sessions/2025-08-30_08-50-17/br-recovery.log' from root:root to 1000:1000

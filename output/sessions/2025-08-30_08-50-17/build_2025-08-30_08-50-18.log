# 2025-08-30 08:50:18
# run hook: build all

[36m==========================================[0m
[36m          Start building all images[0m
[36m==========================================[0m
[35mGenerated blank misc image[0m
[35mDone packing /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/firmware/misc.img[0m
[35mRunning mk-misc.sh - build_misc succeeded.[0m
[36mToolchain for loader (U-Boot):[0m
[36m/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/prebuilts/gcc/linux-x86/aarch64/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-[0m

[36m==========================================[0m
[36m          Start building U-Boot[0m
[36m==========================================[0m
[35m+ cd u-boot[0m
[35m+ ./make.sh CROSS_COMPILE=/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/prebuilts/gcc/linux-x86/aarch64/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu- rv1126b ../rkbin/RKBOOT/RV1126BPMINIALL.ini --spl-new[0m
## make rv1126b_defconfig -j44
#
# configuration written to .config
#
scripts/kconfig/conf  --silentoldconfig Kconfig
  CHK     include/config.h
  CFG     u-boot.cfg
  GEN     include/autoconf.mk.dep
  CFG     spl/u-boot.cfg
  CFG     tpl/u-boot.cfg
  GEN     include/autoconf.mk
  GEN     spl/include/autoconf.mk
  GEN     tpl/include/autoconf.mk
  CHK     include/config/uboot.release
  CHK     include/generated/timestamp_autogenerated.h
  UPD     include/generated/timestamp_autogenerated.h
  UPD     include/config/uboot.release
  CHK     include/generated/version_autogenerated.h
  UPD     include/generated/version_autogenerated.h
  CHK     include/generated/generic-asm-offsets.h
  CHK     include/generated/asm-offsets.h
  HOSTCC  tools/mkenvimage.o
  HOSTCC  tools/fit_image.o
  HOSTCC  tools/image-host.o
  CHK     include/config.h
  HOSTCC  tools/dumpimage.o
  HOSTCC  tools/mkimage.o
  CFG     u-boot.cfg
  HOSTLD  tools/mkenvimage
  HOSTLD  tools/dumpimage
  HOSTLD  tools/mkimage
  CC      arch/arm/cpu/armv8/fwcall.o
  CC      arch/arm/mach-rockchip/board.o
  CC      common/main.o
  LD      arch/arm/cpu/armv8/built-in.o
  CC      cmd/version.o
  CC      drivers/usb/gadget/f_fastboot.o
  LD      common/built-in.o
  LD      cmd/built-in.o
  CC      lib/display_options.o
  LD      arch/arm/mach-rockchip/built-in.o
  LD      lib/built-in.o
  LD      drivers/usb/gadget/built-in.o
  LD      u-boot
  OBJCOPY u-boot.srec
  OBJCOPY u-boot-nodtb.bin
  SYM     u-boot.sym
start=$(/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/prebuilts/gcc/linux-x86/aarch64/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-nm u-boot | grep __rel_dyn_start | cut -f 1 -d ' '); end=$(/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/prebuilts/gcc/linux-x86/aarch64/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-nm u-boot | grep __rel_dyn_end | cut -f 1 -d ' '); tools/relocate-rela u-boot-nodtb.bin 0x40200000 $start $end
make[2]: “arch/arm/dts/rv1126b-evb.dtb”已是最新。
  COPY    u-boot.dtb
  CAT     u-boot-dtb.bin
  MKIMAGE u-boot.img
  MKIMAGE u-boot-dtb.img
  COPY    u-boot.bin
  ALIGN   u-boot.bin
  COPY    spl/u-boot-spl.dtb
  CC      spl/common/spl/spl.o
  CC      tpl/arch/arm/mach-rockchip/tpl.o
  CC      spl/arch/arm/mach-rockchip/spl.o
  CC      spl/arch/arm/cpu/armv8/fwcall.o
  CC      tpl/arch/arm/cpu/armv8/fwcall.o
  CC      spl/lib/display_options.o
  LD      spl/arch/arm/cpu/armv8/built-in.o
  LD      tpl/arch/arm/cpu/armv8/built-in.o
  LD      tpl/arch/arm/mach-rockchip/built-in.o
  LD      spl/arch/arm/mach-rockchip/built-in.o
  LD      spl/common/spl/built-in.o
  LD      spl/lib/built-in.o
  LD      tpl/u-boot-tpl
  OBJCOPY tpl/u-boot-tpl-nodtb.bin
  COPY    tpl/u-boot-tpl.bin
  LD      spl/u-boot-spl
  OBJCOPY spl/u-boot-spl-nodtb.bin
  CAT     spl/u-boot-spl-dtb.bin
  COPY    spl/u-boot-spl.bin
  CFGCHK  u-boot.cfg
pack u-boot.itb okay! Input: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/rkbin/RKTRUST/RV1126BTRUST.ini

FIT description: FIT Image with ATF/OP-TEE/U-Boot/MCU
Created:         Sat Aug 30 08:50:22 2025
 Image 0 (uboot)
  Description:  U-Boot
  Created:      Sat Aug 30 08:50:22 2025
  Type:         Standalone Program
  Compression:  lzma compressed
  Data Size:    349664 Bytes = 341.47 KiB = 0.33 MiB
  Architecture: AArch64
  Load Address: 0x40200000
  Entry Point:  unavailable
  Hash algo:    sha256
  Hash value:   8ac9ca8dc572db954d2016f502ff38b6ee544a7d3cbf41b6ae3476b9a6450988
 Image 1 (atf-1)
  Description:  ARM Trusted Firmware
  Created:      Sat Aug 30 08:50:22 2025
  Type:         Firmware
  Compression:  lzma compressed
  Data Size:    33662 Bytes = 32.87 KiB = 0.03 MiB
  Architecture: AArch64
  Load Address: 0x40000000
  Hash algo:    sha256
  Hash value:   42352af0db7a55a3a46d92d23bf9db03a74ce91bcd91070b0b12449fd326585c
 Image 2 (atf-2)
  Description:  ARM Trusted Firmware
  Created:      Sat Aug 30 08:50:22 2025
  Type:         Firmware
  Compression:  uncompressed
  Data Size:    8192 Bytes = 8.00 KiB = 0.01 MiB
  Architecture: AArch64
  Load Address: 0x3ffbb000
  Hash algo:    sha256
  Hash value:   8736494db579a3e6fcce3be9666f40885e99cc0775c66042736b2b1d70564942
 Image 3 (atf-3)
  Description:  ARM Trusted Firmware
  Created:      Sat Aug 30 08:50:22 2025
  Type:         Firmware
  Compression:  uncompressed
  Data Size:    6764 Bytes = 6.61 KiB = 0.01 MiB
  Architecture: AArch64
  Load Address: 0x3ff1e000
  Hash algo:    sha256
  Hash value:   c3d9e784a50bcf5e860355fe03eb9293f81ffcb010e89ec4ea7f6a70ea793fb3
 Image 4 (atf-4)
  Description:  ARM Trusted Firmware
  Created:      Sat Aug 30 08:50:22 2025
  Type:         Firmware
  Compression:  uncompressed
  Data Size:    4096 Bytes = 4.00 KiB = 0.00 MiB
  Architecture: AArch64
  Load Address: 0x3ffbd000
  Hash algo:    sha256
  Hash value:   8cb1ae2802d90197b9d860fbbca838237a7e67b00d220f9ac3970c4af8e1eb7c
 Image 5 (fdt)
  Description:  U-Boot dtb
  Created:      Sat Aug 30 08:50:22 2025
  Type:         Flat Device Tree
  Compression:  uncompressed
  Data Size:    7585 Bytes = 7.41 KiB = 0.01 MiB
  Architecture: AArch64
  Hash algo:    sha256
  Hash value:   bc038548e056a130b3b176015c2fd5fba6301121cc627a5e5a34c93ca0e8705e
 Default Configuration: 'conf'
 Configuration 0 (conf)
  Description:  rv1126b-evb
  Kernel:       unavailable
  Firmware:     atf-1
  FDT:          fdt
  Loadables:    uboot
                atf-2
                atf-3
                atf-4
********boot_merger ver 1.35********
Info:Pack loader ok.
pack loader(SPL) okay! Input: ../rkbin/RKBOOT/RV1126BPMINIALL.ini

/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/u-boot
pack loader with new: spl/u-boot-spl.bin

Image(no-signed, version=0): uboot.img (FIT with uboot, trust...) is ready
Image(no-signed): rv1126bp_spl_loader_v1.03.103.bin (with spl, ddr...) is ready
pack uboot.img okay! Input: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/rkbin/RKTRUST/RV1126BTRUST.ini

Platform RV1126B is build OK, with new .config(make rv1126b_defconfig -j44)
/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/prebuilts/gcc/linux-x86/aarch64/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-
Sat Aug 30 08:50:22 CST 2025
[35m+ cd ..[0m
[35mRunning mk-loader.sh - build_uboot succeeded.[0m
[35mUsing kernel version(6.1) from environment[0m
[36mToolchain for kernel:[0m
[36m/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/prebuilts/gcc/linux-x86/aarch64/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-[0m

[36m==========================================[0m
[36m          Start building kernel[0m
[36m==========================================[0m
[36m# Found kernel's basic config fragment: rv1126b.config[0m
[35m+ make -C /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/kernel/ -j23 CROSS_COMPILE=/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/prebuilts/gcc/linux-x86/aarch64/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu- ARCH=arm64 rv1126b_defconfig rv1126b.config[0m
make: Entering directory '/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/kernel-6.1'
#
# configuration written to .config
#
Using .config as base
Merging ./arch/arm64/configs/rv1126b.config
Value of CONFIG_ROCKCHIP_RKNPU_PROC_FS is redefined by fragment ./arch/arm64/configs/rv1126b.config:
Previous value: # CONFIG_ROCKCHIP_RKNPU_PROC_FS is not set
New value: CONFIG_ROCKCHIP_RKNPU_PROC_FS=y

Value of CONFIG_ROCKCHIP_RKNPU_DMA_HEAP is redefined by fragment ./arch/arm64/configs/rv1126b.config:
Previous value: # CONFIG_ROCKCHIP_RKNPU_DMA_HEAP is not set
New value: CONFIG_ROCKCHIP_RKNPU_DMA_HEAP=y

#
# merged configuration written to .config (needs make)
#
.config:5820:warning: override: ROCKCHIP_RKNPU_DMA_HEAP changes choice state
#
# configuration written to .config
#
make: Leaving directory '/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/kernel-6.1'
[35m+ make -C /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/kernel/ -j23 CROSS_COMPILE=/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/prebuilts/gcc/linux-x86/aarch64/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu- ARCH=arm64 rv1126bp-evb-v14.img[0m
make: Entering directory '/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/kernel-6.1'
  SYNC    include/config/auto.conf.cmd
  UPD     include/generated/compile.h
  CALL    scripts/checksyscalls.sh
  CC      init/version.o
  AR      init/built-in.a
  AR      built-in.a
  AR      vmlinux.a
  LD      vmlinux.o
  OBJCOPY modules.builtin.modinfo
  GEN     modules.builtin
  MODPOST Module.symvers
  UPD     include/generated/utsversion.h
  CC      init/version-timestamp.o
  LD      .tmp_vmlinux.kallsyms1
  NM      .tmp_vmlinux.kallsyms1.syms
  KSYMS   .tmp_vmlinux.kallsyms1.S
  AS      .tmp_vmlinux.kallsyms1.o
  LD      .tmp_vmlinux.kallsyms2
  NM      .tmp_vmlinux.kallsyms2.syms
  KSYMS   .tmp_vmlinux.kallsyms2.S
  AS      .tmp_vmlinux.kallsyms2.o
  LD      vmlinux
  NM      System.map
  SORTTAB vmlinux
  OBJCOPY arch/arm64/boot/Image
  LZ4C    arch/arm64/boot/Image.lz4
  Image:  resource.img (with rv1126bp-evb-v14.dtb logo.bmp logo_kernel.bmp) is ready
  Image:  boot.img (with Image  resource.img) is ready
  Image:  zboot.img (with Image.lz4  resource.img) is ready
make: Leaving directory '/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/kernel-6.1'
[35m+ /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/scripts/mk-fitimage.sh kernel/boot.img /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/.chip/boot.its kernel/arch/arm64/boot/Image kernel/arch/arm64/boot/dts/rockchip/rv1126bp-evb-v14.dtb kernel/resource.img[0m
FIT description: U-Boot FIT source file for arm
Created:         Sat Aug 30 08:50:35 2025
 Image 0 (fdt)
  Description:  unavailable
  Created:      Sat Aug 30 08:50:35 2025
  Type:         Flat Device Tree
  Compression:  uncompressed
  Data Size:    68490 Bytes = 66.88 KiB = 0.07 MiB
  Architecture: AArch64
  Load Address: 0xffffff00
  Hash algo:    sha256
  Hash value:   1c05afdd29fb6e150db47a6b11abacaa978ceda0bc748ae9806164800c1c8046
 Image 1 (kernel)
  Description:  unavailable
  Created:      Sat Aug 30 08:50:35 2025
  Type:         Kernel Image
  Compression:  uncompressed
  Data Size:    13174792 Bytes = 12866.01 KiB = 12.56 MiB
  Architecture: AArch64
  OS:           Linux
  Load Address: 0xffffff01
  Entry Point:  0xffffff01
  Hash algo:    sha256
  Hash value:   48b5cf1c71fbe30142bc7329eab06d1896747def7552b15d9af50c63e2f604f2
 Image 2 (resource)
  Description:  unavailable
  Created:      Sat Aug 30 08:50:35 2025
  Type:         Multi-File Image
  Compression:  uncompressed
  Data Size:    106496 Bytes = 104.00 KiB = 0.10 MiB
  Hash algo:    sha256
  Hash value:   30d8be602c2723199411a361c4689606b2077c737053dad3f84fb8d8a696e9d4
 Default Configuration: 'conf'
 Configuration 0 (conf)
  Description:  unavailable
  Kernel:       kernel
  FDT:          fdt
[35m+ ln -rsf kernel/boot.img /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/firmware/boot.img[0m
Not Found io-domains in kernel/arch/arm64/boot/dts/rockchip/rv1126bp-evb-v14.dts
[35mRunning mk-kernel.sh - build_kernel succeeded.[0m
[36m==========================================[0m
[36m          Start building rootfs(buildroot)[0m
[36m==========================================[0m
[36m==========================================[0m
[36m          Start building buildroot(2024.02)[0m
[36m==========================================[0m
[35m[0mmake: Entering directory '/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot'
Parsing defconfig: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/configs/rockchip_rv1126b_ipc_defconfig
Using configs/rockchip/base/kernel.config as base
Merging configs/rockchip/fs/e2fs.config
Merging configs/rockchip/base/common.config
Merging configs/rockchip/base/base.config
Merging configs/rockchip/wifibt/bt.config
Merging configs/rockchip/network/network.config
Merging configs/rockchip/wifibt/wireless.config
Merging configs/rockchip/chips/rv1126b.config
Value of BR2_ROOTFS_OVERLAY is redefined by configs/rockchip/chips/rv1126b.config:
Previous value:	BR2_ROOTFS_OVERLAY="board/rockchip/common/base"
Modify value:	BR2_ROOTFS_OVERLAY+="board/rockchip/rv1126b/fs-overlay/"
New value:	BR2_ROOTFS_OVERLAY="board/rockchip/common/base board/rockchip/rv1126b/fs-overlay/"

Merging configs/rockchip/chips/rv1126b_aarch64.config
Merging configs/rockchip/multimedia/mpp.config
Merging configs/rockchip/multimedia/audio.config
Merging configs/rockchip/multimedia/camera.config
Merging /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/configs/rockchip_rv1126b_ipc_defconfig
Value of BR2_PACKAGE_ALSA_UCM_CONF is redefined by /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/configs/rockchip_rv1126b_ipc_defconfig:
Previous value:	BR2_PACKAGE_ALSA_UCM_CONF=y
Modify value:	# BR2_PACKAGE_ALSA_UCM_CONF is reset to default
New value:	

Value of BR2_PACKAGE_ALSA_UTILS is redefined by /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/configs/rockchip_rv1126b_ipc_defconfig:
Previous value:	BR2_PACKAGE_ALSA_UTILS=y
Modify value:	# BR2_PACKAGE_ALSA_UTILS is reset to default
New value:	

Value of BR2_PACKAGE_LIBMAD is redefined by /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/configs/rockchip_rv1126b_ipc_defconfig:
Previous value:	BR2_PACKAGE_LIBMAD=y
Modify value:	# BR2_PACKAGE_LIBMAD is reset to default
New value:	

Value of BR2_PACKAGE_PULSEAUDIO is redefined by /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/configs/rockchip_rv1126b_ipc_defconfig:
Previous value:	BR2_PACKAGE_PULSEAUDIO=y
Modify value:	# BR2_PACKAGE_PULSEAUDIO is reset to default
New value:	

Value of BR2_ROOTFS_OVERLAY is redefined by /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/configs/rockchip_rv1126b_ipc_defconfig:
Previous value:	BR2_ROOTFS_OVERLAY="board/rockchip/common/base board/rockchip/rv1126b/fs-overlay/"
New value:	BR2_ROOTFS_OVERLAY="board/rockchip/common/base board/rockchip/rv1126b/fs-overlay-ipc/"

#
# merged configuration written to /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/.config.in (needs make)
#
#
# configuration written to /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/.config
#
make: Leaving directory '/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot'
3c3
< # Buildroot 2024.02 Configuration
---
> # Buildroot linux-6.1-stan-rkr6.1 Configuration
[34mBuildroot config changed![0m
[34mYou might need to clean it before building:[0m
[34mrm -rf /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc
[0m
2025-08-30T08:50:42 [7m>>>   Finalizing host directory[27m
2025-08-30T08:50:42 [7m>>>   Finalizing target directory[27m
2025-08-30T08:50:43 [7m>>>   Sanitizing RPATH in target tree[27m
2025-08-30T08:50:45 [7m>>>   Sanity check in overlay board/rockchip/common/base[27m
2025-08-30T08:50:45 [7m>>>   Sanity check in overlay board/rockchip/rv1126b/fs-overlay-ipc/[27m
2025-08-30T08:50:45 [7m>>>   Copying overlay board/rockchip/common/base[27m
2025-08-30T08:50:45 [7m>>>   Copying overlay board/rockchip/rv1126b/fs-overlay-ipc/[27m
2025-08-30T08:50:45 [7m>>>   Executing post-build script board/rockchip/common/post-build.sh[27m
2025-08-30T08:50:45 >>> Ignored board/rockchip/common/overlays/10-weston
2025-08-30T08:50:45 >>> Ignored board/rockchip/common/overlays/11-weston-chromium
2025-08-30T08:50:45 >>> Copying board/rockchip/common/overlays/20-wlan0
2025-08-30T08:50:45 >>> Copying board/rockchip/common/overlays/default
2025-08-30T08:50:45 >>> [35mRunning within sudo(root) environment![0m
2025-08-30T08:50:45 >>> [35mRunning 00-wifibt.sh for /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target (buildroot init=busybox)...[0m
2025-08-30T08:50:45 >>> [36mBuilding Wifi/BT module and firmwares...[0m
2025-08-30T08:50:45 >>> [35mRunning 01-hostname.sh for /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target (buildroot init=busybox)...[0m
2025-08-30T08:50:45 >>> [35mRunning 10-os-release.sh for /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target (buildroot init=busybox)...[0m
2025-08-30T08:50:45 >>> [35mRunning 20-info.sh for /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target (buildroot init=busybox)...[0m
2025-08-30T08:50:45 >>> [35mRunning 30-fstab.sh for /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target (buildroot init=busybox)...[0m
2025-08-30T08:50:45 >>> [35mRunning 40-busybox-reboot.sh for /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target (buildroot init=busybox)...[0m
2025-08-30T08:50:45 >>> [35mRunning 50-locale.sh for /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target (buildroot init=busybox)...[0m
2025-08-30T08:50:45 >>> [35mRunning 90-overlay.sh for /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target (buildroot init=busybox)...[0m
2025-08-30T08:50:45 >>> [35mHandling overlay: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/overlays/common/default)...[0m
2025-08-30T08:50:45 >>> [35mInstalling overlay: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/overlays/common/udev-rules to /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target...[0m
2025-08-30T08:50:46 >>> [35mHandling overlay: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/overlays/rootfs/async-commit)...[0m
2025-08-30T08:50:46 >>> [35mHandling overlay: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/overlays/rootfs/bootanim)...[0m
2025-08-30T08:50:46 >>> [35mHandling overlay: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/overlays/rootfs/chromium)...[0m
2025-08-30T08:50:46 >>> [35mInstalling overlay: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/overlays/rootfs/default to /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target...[0m
2025-08-30T08:50:46 >>> [35mHandling overlay: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/overlays/rootfs/disk-helpers)...[0m
2025-08-30T08:50:46 >>> [36mInstalling mount service...[0m
2025-08-30T08:50:46 >>> [35mHandling overlay: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/fonts)...[0m
2025-08-30T08:50:46 >>> [35mHandling overlay: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/overlays/rootfs/frecon)...[0m
2025-08-30T08:50:46 >>> [35mHandling overlay: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/overlays/rootfs/fstrim)...[0m
2025-08-30T08:50:46 >>> [35mHandling overlay: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/overlays/rootfs/generate-logs)...[0m
2025-08-30T08:50:46 >>> [35mHandling overlay: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/overlays/rootfs/input-event-daemon)...[0m
2025-08-30T08:50:46 >>> [35mHandling overlay: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/overlays/rootfs/irqbalance)...[0m
2025-08-30T08:50:46 >>> [35mHandling overlay: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/overlays/rootfs/log-guardian)...[0m
2025-08-30T08:50:46 >>> [35mHandling overlay: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/tools)...[0m
2025-08-30T08:50:46 >>> [35mHandling overlay: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/overlays/rootfs/usb-gadget)...[0m
2025-08-30T08:50:46 >>> [36mInstalling USB gadget service...[0m
2025-08-30T08:50:46 >>> [36mInstalling ADBD...[0m
2025-08-30T08:50:46 >>> [35mRunning 91-modules.sh for /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target (buildroot init=busybox)...[0m
2025-08-30T08:50:46 >>> [35mRunning 95-extra-parts.sh for /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target (buildroot init=busybox)...[0m
2025-08-30T08:50:46 >>> [35mRunning 97-ldcache.sh for /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target (buildroot init=busybox)...[0m
2025-08-30T08:50:46 >>> [35mRunning 99-owner.sh for /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target (buildroot init=busybox)...[0m
2025-08-30T08:50:46 >>> [35mRunning build.sh - post-rootfs succeeded.[0m
2025-08-30T08:50:46 [7m>>>   Generating root filesystems common tables[27m
2025-08-30T08:50:46 [7m>>>   Generating filesystem image rootfs.cpio[27m
2025-08-30T08:51:01 [7m>>>   Generating filesystem image rootfs.ext2[27m
2025-08-30T08:51:02 [7m>>>   Generating filesystem image rootfs.squashfs[27m
2025-08-30T08:51:05 [7m>>>   Generating filesystem image rootfs.tar[27m
Done in 29s
[35mLog saved on /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/sessions/2025-08-30_08-50-17/br-rockchip_rv1126b_ipc_2025-08-30_08-50-37.log[0m
[35mGenerated images:[0m
/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/buildroot/images/rootfs.cpio
/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/buildroot/images/rootfs.cpio.gz
/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/buildroot/images/rootfs.ext2
/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/buildroot/images/rootfs.ext4
/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/buildroot/images/rootfs.squashfs
/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/buildroot/images/rootfs.tar
# 2025-08-30 08:50:45
# run hook: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target

[35mRunning 00-wifibt.sh for /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target (buildroot init=busybox)...[0m
[36mBuilding Wifi/BT module and firmwares...[0m
[35mSkipping 00-wifibt.sh - build_wifibt for missing configs:  RK_WIFIBT RK_WIFIBT_MODULES.[0m
[35mRunning 01-hostname.sh for /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target (buildroot init=busybox)...[0m
[36mSetting hostname: rv1126bp-buildroot[0m
[35mRunning 10-os-release.sh for /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target (buildroot init=busybox)...[0m
[36mAdding information to /etc/os-release...[0m
[35mRunning 20-info.sh for /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target (buildroot init=busybox)...[0m
[35mRunning 30-fstab.sh for /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target (buildroot init=busybox)...[0m
[36mFixing up /etc/fstab...[0m
[36mFixing up rootfs type: ext4[0m
[36mFixup basic partitions for non-systemd init...[0m
[36mFixing up basic partition: proc /proc[0m
[36mFixing up basic partition: devtmpfs /dev[0m
[36mFixing up basic partition: devpts /dev/pts mode=0620,ptmxmode=0000,gid=5[0m
[36mFixing up basic partition: tmpfs /dev/shm nosuid,nodev,noexec[0m
[36mFixing up basic partition: sysfs /sys nosuid,nodev,noexec[0m
[36mFixing up basic partition: configfs /sys/kernel/config[0m
[36mFixing up basic partition: debugfs /sys/kernel/debug[0m
[36mFixing up basic partition: pstore /sys/fs/pstore nosuid,nodev,noexec[0m
[36mFixing up device partition: PARTLABEL=oem /oem ext4 defaults[0m
[36mFixing up device partition: PARTLABEL=userdata /userdata ext4 defaults[0m
[35mRunning 40-busybox-reboot.sh for /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target (buildroot init=busybox)...[0m
[35mRunning 50-locale.sh for /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target (buildroot init=busybox)...[0m
[36mSetting LANG environment to en_US.UTF-8...[0m
[35mRunning 90-overlay.sh for /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target (buildroot init=busybox)...[0m
[35mHandling overlay: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/overlays/common/default)...[0m
sending incremental file list
./

sent 53 bytes  received 19 bytes  144.00 bytes/sec
total size is 0  speedup is 0.00
[35mInstalling overlay: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/overlays/common/udev-rules to /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target...[0m
sending incremental file list
lib/
lib/udev/
lib/udev/rules.d/

sent 229 bytes  received 24 bytes  506.00 bytes/sec
total size is 1,854  speedup is 7.33
[35mHandling overlay: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/overlays/rootfs/async-commit)...[0m
[35mHandling overlay: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/overlays/rootfs/bootanim)...[0m
[35mHandling overlay: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/overlays/rootfs/chromium)...[0m
[35mInstalling overlay: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/overlays/rootfs/default to /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target...[0m
sending incremental file list
./

sent 49 bytes  received 19 bytes  136.00 bytes/sec
total size is 0  speedup is 0.00
[35mHandling overlay: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/overlays/rootfs/disk-helpers)...[0m
[36mInstalling mount service...[0m
sending incremental file list
usr/
usr/bin/

sent 158 bytes  received 24 bytes  364.00 bytes/sec
total size is 13,953  speedup is 76.66
[35mHandling overlay: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/fonts)...[0m
[35mNo extra fonts for buildroot by default[0m
[35mHandling overlay: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/overlays/rootfs/frecon)...[0m
[35mHandling overlay: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/overlays/rootfs/fstrim)...[0m
[35mHandling overlay: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/overlays/rootfs/generate-logs)...[0m
[35mHandling overlay: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/overlays/rootfs/input-event-daemon)...[0m
[35mDisabling input-event-daemon...[0m
[35mHandling overlay: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/overlays/rootfs/irqbalance)...[0m
[35mHandling overlay: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/overlays/rootfs/log-guardian)...[0m
[35mHandling overlay: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/tools)...[0m
[35mHandling overlay: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/overlays/rootfs/usb-gadget)...[0m
[36mInstalling USB gadget service...[0m
[36mUSB gadget functions: adb[0m
[36mInstalling ADBD...[0m
sending incremental file list
lib/
lib/udev/
lib/udev/rules.d/
usr/bin/
usr/bin/usbdevice

sent 17,391 bytes  received 52 bytes  34,886.00 bytes/sec
total size is 17,268  speedup is 0.99
[35mRunning 91-modules.sh for /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target (buildroot init=busybox)...[0m
[36mStrip kernel modules...[0m
copy from `/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target/usr/lib/module/rockit.ko' [elf64-littleaarch64] to `/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target/usr/lib/module/stkeg49P' [elf64-littleaarch64]
copy from `/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target/usr/lib/module/kmpp.ko' [elf64-littleaarch64] to `/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target/usr/lib/module/st4xbH0R' [elf64-littleaarch64]
[35mRunning 95-extra-parts.sh for /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target (buildroot init=busybox)...[0m
[36mPreparing extra partitions...[0m
[35mRunning 97-ldcache.sh for /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target (buildroot init=busybox)...[0m
[35mRunning 99-owner.sh for /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target (buildroot init=busybox)...[0m
[36mFixing up owner for /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output...[0m
changed ownership of '/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/misc.img' from root:root to 1000:1000
changed ownership of '/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/.make_usage' from root:root to 1000:1000
changed ownership of '/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/.parsed_cmds' from root:root to 1000:1000
changed ownership of '/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/rootfs' from root:root to 1000:1000
changed ownership of '/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/firmware' from root:root to 1000:1000
changed ownership of '/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/firmware/misc.img' from root:root to 1000:1000
changed ownership of '/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/firmware/uboot.img' from root:root to 1000:1000
changed ownership of '/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/firmware/MiniLoaderAll.bin' from root:root to 1000:1000
changed ownership of '/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/firmware/boot.img' from root:root to 1000:1000
changed ownership of '/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/final.env' from root:root to 1000:1000
changed ownership of '/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/extra-parts/userdata-mount' from root:root to 1000:1000
changed ownership of '/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/extra-parts/oem-mount' from root:root to 1000:1000
changed ownership of '/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/initial.env' from root:root to 1000:1000
changed ownership of '/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/log' from root:root to 1000:1000
changed ownership of '/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/buildroot' from root:root to 1000:1000
changed ownership of '/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/buildroot/.stamp_build_start' from root:root to 1000:1000
changed ownership of '/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/buildroot/images' from root:root to 1000:1000
changed ownership of '/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/sessions/2025-08-30_08-50-17' from root:root to 1000:1000
changed ownership of '/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/sessions/2025-08-30_08-50-17/part-table' from root:root to 1000:1000
changed ownership of '/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/sessions/2025-08-30_08-50-17/.config' from root:root to 1000:1000
changed ownership of '/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/sessions/2025-08-30_08-50-17/br-rockchip_rv1126b_ipc.log' from root:root to 1000:1000
changed ownership of '/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/sessions/2025-08-30_08-50-17/post-rootfs_2025-08-30_08-50-45.log' from root:root to 1000:1000
changed ownership of '/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/sessions/2025-08-30_08-50-17/build_2025-08-30_08-50-18.log' from root:root to 1000:1000
changed ownership of '/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/sessions/2025-08-30_08-50-17/final.env' from root:root to 1000:1000
changed ownership of '/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/sessions/2025-08-30_08-50-17/post-rootfs.log' from root:root to 1000:1000
changed ownership of '/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/sessions/2025-08-30_08-50-17/.stamp_post_start' from root:root to 1000:1000
changed ownership of '/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/sessions/2025-08-30_08-50-17/99-all-build.log' from root:root to 1000:1000
changed ownership of '/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/sessions/2025-08-30_08-50-17/br-rootfs.log' from root:root to 1000:1000
changed ownership of '/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/sessions/2025-08-30_08-50-17/initial.env' from root:root to 1000:1000
changed ownership of '/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/sessions/2025-08-30_08-50-17/br.log' from root:root to 1000:1000
changed ownership of '/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/sessions/2025-08-30_08-50-17/99-all-build_2025-08-30_08-50-18.log' from root:root to 1000:1000
changed ownership of '/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/sessions/2025-08-30_08-50-17/br-rockchip_rv1126b_ipc_2025-08-30_08-50-37.log' from root:root to 1000:1000
changed ownership of '/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/sessions/2025-08-30_08-50-17/build.log' from root:root to 1000:1000
changed ownership of '/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/sessions/latest' from root:root to 1000:1000
[35mRunning mk-rootfs.sh - build_buildroot /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/buildroot/images succeeded.[0m
[35mRunning mk-rootfs.sh - build_rootfs succeeded.[0m
[36m==========================================[0m
[36m          Start building recovery(buildroot)[0m
[36m==========================================[0m
[35m[0mmake: Entering directory '/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot'
Parsing defconfig: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/configs/rockchip_rv1126b_recovery_defconfig
Using configs/rockchip/base/kernel.config as base
Merging configs/rockchip/fs/e2fs.config
Merging configs/rockchip/base/common.config
Merging configs/rockchip/base/base.config
Merging configs/rockchip/base/kernel.config
Merging configs/rockchip/fs/e2fs.config
Merging configs/rockchip/base/common.config
Merging configs/rockchip/fs/vfat.config
Merging configs/rockchip/base/recovery.config
Merging configs/rockchip/chips/rv1126b.config
Value of BR2_ROOTFS_OVERLAY is redefined by configs/rockchip/chips/rv1126b.config:
Previous value:	BR2_ROOTFS_OVERLAY="board/rockchip/common/base"
Modify value:	BR2_ROOTFS_OVERLAY+="board/rockchip/rv1126b/fs-overlay/"
New value:	BR2_ROOTFS_OVERLAY="board/rockchip/common/base board/rockchip/rv1126b/fs-overlay/"

Merging configs/rockchip/chips/rv1126b_aarch64.config
Merging /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/configs/rockchip_rv1126b_recovery_defconfig
#
# merged configuration written to /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/.config.in (needs make)
#
#
# configuration written to /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/.config
#
make: Leaving directory '/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot'
3c3
< # Buildroot 2024.02 Configuration
---
> # Buildroot linux-6.1-stan-rkr6.1 Configuration
[34mBuildroot config changed![0m
[34mYou might need to clean it before building:[0m
[34mrm -rf /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery
[0m
2025-08-30T08:51:13 [7m>>>   Finalizing host directory[27m
2025-08-30T08:51:13 [7m>>>   Finalizing target directory[27m
2025-08-30T08:51:13 [7m>>>   Sanitizing RPATH in target tree[27m
2025-08-30T08:51:13 [7m>>>   Sanity check in overlay board/rockchip/common/base[27m
2025-08-30T08:51:13 [7m>>>   Sanity check in overlay board/rockchip/rv1126b/fs-overlay/[27m
2025-08-30T08:51:13 [7m>>>   Copying overlay board/rockchip/common/base[27m
2025-08-30T08:51:13 [7m>>>   Copying overlay board/rockchip/rv1126b/fs-overlay/[27m
2025-08-30T08:51:13 [7m>>>   Executing post-build script board/rockchip/common/post-build.sh[27m
2025-08-30T08:51:13 >>> Ignored board/rockchip/common/overlays/10-weston
2025-08-30T08:51:13 >>> Ignored board/rockchip/common/overlays/11-weston-chromium
2025-08-30T08:51:13 >>> Ignored board/rockchip/common/overlays/20-wlan0
2025-08-30T08:51:13 >>> Copying board/rockchip/common/overlays/default
2025-08-30T08:51:14 >>> [35mRunning within sudo(root) environment![0m
2025-08-30T08:51:14 >>> [35mRunning 01-hostname.sh for /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/target (recovery init=busybox)...[0m
2025-08-30T08:51:14 >>> [35mRunning 10-os-release.sh for /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/target (recovery init=busybox)...[0m
2025-08-30T08:51:14 >>> [35mRunning 30-fstab.sh for /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/target (recovery init=busybox)...[0m
2025-08-30T08:51:14 >>> [35mRunning 40-busybox-reboot.sh for /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/target (recovery init=busybox)...[0m
2025-08-30T08:51:14 >>> [35mRunning 50-locale.sh for /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/target (recovery init=busybox)...[0m
2025-08-30T08:51:14 >>> [35mRunning 90-overlay.sh for /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/target (recovery init=busybox)...[0m
2025-08-30T08:51:14 >>> [35mHandling overlay: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/overlays/common/default)...[0m
2025-08-30T08:51:14 >>> [35mInstalling overlay: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/overlays/common/udev-rules to /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/target...[0m
2025-08-30T08:51:14 >>> [35mHandling overlay: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/overlays/recovery/adbd)...[0m
2025-08-30T08:51:14 >>> [35mHandling overlay: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/overlays/recovery/default)...[0m
2025-08-30T08:51:14 >>> [35mRunning 91-modules.sh for /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/target (recovery init=busybox)...[0m
2025-08-30T08:51:14 >>> [35mRunning 97-ldcache.sh for /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/target (recovery init=busybox)...[0m
2025-08-30T08:51:14 >>> [35mRunning 99-owner.sh for /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_recovery/target (recovery init=busybox)...[0m
2025-08-30T08:51:14 >>> [35mRunning build.sh - post-rootfs succeeded.[0m
2025-08-30T08:51:14 [7m>>>   Generating root filesystems common tables[27m
2025-08-30T08:51:14 [7m>>>   Generating filesystem image rootfs.cpio[27m
2025-08-30T08:51:16 [7m>>>   Generating filesystem image rootfs.ext2[27m
2025-08-30T08:51:17 [7m>>>   Generating filesystem image rootfs.squashfs[27m
2025-08-30T08:51:17 [7m>>>   Generating filesystem image rootfs.tar[27m
Done in 10s
[35mLog saved on /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/sessions/2025-08-30_08-50-17/br-rockchip_rv1126b_recovery_2025-08-30_08-51-08.log[0m
[35mGenerated images:[0m
/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/recovery/images/rootfs.cpio
/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/recovery/images/rootfs.cpio.gz
/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/recovery/images/rootfs.ext2
/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/recovery/images/rootfs.ext4
/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/recovery/images/rootfs.squashfs
/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/recovery/images/rootfs.tar
[35mUsing kernel version(6.1) from environment[0m
[36mToolchain for kernel:[0m
[36m/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/prebuilts/gcc/linux-x86/aarch64/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-[0m

[36m==========================================[0m
[36m          Start building recovery-kernel[0m
[36m==========================================[0m
[35m+ rm -rf /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/recovery-kernel[0m
[35m+ ln -rsf /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/kernel /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/recovery-kernel[0m
[35m+ cd /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/recovery-kernel[0m
[36m# Found kernel's basic config fragment: rv1126b.config[0m
[35m+ make -C /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/kernel/ -j23 CROSS_COMPILE=/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/prebuilts/gcc/linux-x86/aarch64/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu- ARCH=arm64 rv1126b_defconfig rv1126b.config[0m
make: Entering directory '/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/kernel-6.1'
#
# configuration written to .config
#
Using .config as base
Merging ./arch/arm64/configs/rv1126b.config
Value of CONFIG_ROCKCHIP_RKNPU_PROC_FS is redefined by fragment ./arch/arm64/configs/rv1126b.config:
Previous value: # CONFIG_ROCKCHIP_RKNPU_PROC_FS is not set
New value: CONFIG_ROCKCHIP_RKNPU_PROC_FS=y

Value of CONFIG_ROCKCHIP_RKNPU_DMA_HEAP is redefined by fragment ./arch/arm64/configs/rv1126b.config:
Previous value: # CONFIG_ROCKCHIP_RKNPU_DMA_HEAP is not set
New value: CONFIG_ROCKCHIP_RKNPU_DMA_HEAP=y

#
# merged configuration written to .config (needs make)
#
.config:5820:warning: override: ROCKCHIP_RKNPU_DMA_HEAP changes choice state
#
# configuration written to .config
#
make: Leaving directory '/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/kernel-6.1'
[35m+ make -C /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/kernel/ -j23 CROSS_COMPILE=/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/prebuilts/gcc/linux-x86/aarch64/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu- ARCH=arm64 Image[0m
make: Entering directory '/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/kernel-6.1'
  SYNC    include/config/auto.conf.cmd
  CALL    scripts/checksyscalls.sh
  MODPOST vmlinux.symvers
make: Leaving directory '/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/kernel-6.1'
[35m+ make -C /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/kernel/ -j23 CROSS_COMPILE=/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/prebuilts/gcc/linux-x86/aarch64/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu- ARCH=arm64 rockchip/rv1126bp-evb-v14.dtb[0m
make: Entering directory '/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/kernel-6.1'
make: Leaving directory '/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/kernel-6.1'
[35m+ ln -rsf arch/arm64/boot/Image /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/recovery-kernel.img[0m
[35m+ ln -rsf arch/arm64/boot/dts/rockchip/rv1126bp-evb-v14.dtb /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/recovery-kernel.dtb[0m
[35m+ scripts/resource_tool arch/arm64/boot/dts/rockchip/rv1126bp-evb-v14.dtb logo.bmp logo_kernel.bmp[0m
Pack to resource.img successed!
[35m+ ln -rsf resource.img /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/recovery-resource.img[0m
[35mRunning mk-kernel.sh - build_recovery-kernel succeeded.[0m
Packing /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/recovery/ramboot.img...
FIT description: U-Boot FIT source file for arm
Created:         Sat Aug 30 08:51:22 2025
 Image 0 (fdt)
  Description:  unavailable
  Created:      Sat Aug 30 08:51:22 2025
  Type:         Flat Device Tree
  Compression:  uncompressed
  Data Size:    68490 Bytes = 66.88 KiB = 0.07 MiB
  Architecture: AArch64
  Load Address: 0xffffff00
  Hash algo:    sha256
  Hash value:   1c05afdd29fb6e150db47a6b11abacaa978ceda0bc748ae9806164800c1c8046
 Image 1 (kernel)
  Description:  unavailable
  Created:      Sat Aug 30 08:51:22 2025
  Type:         Kernel Image
  Compression:  uncompressed
  Data Size:    13174792 Bytes = 12866.01 KiB = 12.56 MiB
  Architecture: AArch64
  OS:           Linux
  Load Address: 0xffffff01
  Entry Point:  0xffffff01
  Hash algo:    sha256
  Hash value:   48b5cf1c71fbe30142bc7329eab06d1896747def7552b15d9af50c63e2f604f2
 Image 2 (ramdisk)
  Description:  unavailable
  Created:      Sat Aug 30 08:51:22 2025
  Type:         RAMDisk Image
  Compression:  uncompressed
  Data Size:    5646440 Bytes = 5514.10 KiB = 5.38 MiB
  Architecture: AArch64
  OS:           Linux
  Load Address: 0xffffff02
  Entry Point:  unavailable
  Hash algo:    sha256
  Hash value:   77f070f63f876cffc1cbebd65785bf23016953b4695e8d00445e7d238a319627
 Image 3 (resource)
  Description:  unavailable
  Created:      Sat Aug 30 08:51:22 2025
  Type:         Multi-File Image
  Compression:  uncompressed
  Data Size:    106496 Bytes = 104.00 KiB = 0.10 MiB
  Hash algo:    sha256
  Hash value:   30d8be602c2723199411a361c4689606b2077c737053dad3f84fb8d8a696e9d4
 Default Configuration: 'conf'
 Configuration 0 (conf)
  Description:  unavailable
  Kernel:       kernel
  Init Ramdisk: ramdisk
  FDT:          fdt
[35mRunning mk-recovery.sh - build_recovery succeeded.[0m
[36m==========================================[0m
[36m          Start packing firmwares[0m
[36m==========================================[0m
[36mLinking /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/firmware/parameter.txt from /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/.chip/parameter.txt...[0m
[35mPreparing partiton oem[0m
[36mMerging /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/extra-parts/oem/normal into /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/extra-parts/oem[0m
[35mPacking /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/extra-parts/oem.img from /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/extra-parts/oem.fs[0m
Using host tools in /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/latest/host (except for mke2fs)
Making /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/extra-parts/oem.img from /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/extra-parts/oem (auto sized)
Making /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/extra-parts/oem.img from /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/extra-parts/oem with size(13656KB)
mke2fs 1.47.0 (5-Feb-2023)
Creating regular file /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/extra-parts/oem.img
Creating filesystem with 3414 4k blocks and 3424 inodes

Allocating group tables: 0/1   done                            
Writing inode tables: 0/1   done                            
Creating journal (1024 blocks): done
Copying files into the device: __populate_fs: Could not allocate block in ext2 filesystem while writing file "game_test.gba"
mke2fs: Could not allocate block in ext2 filesystem while populating file system
Retring with increased size....(1/20)
Making /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/extra-parts/oem.img from /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/extra-parts/oem with size(17752KB)
mke2fs 1.47.0 (5-Feb-2023)
Creating regular file /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/extra-parts/oem.img
Creating filesystem with 4438 4k blocks and 4448 inodes

Allocating group tables: 0/1   done                            
Writing inode tables: 0/1   done                            
Creating journal (1024 blocks): done
Copying files into the device: done
Writing superblocks and filesystem accounting information: 0/1   done

tune2fs 1.47.0 (5-Feb-2023)
Setting maximal mount count to -1
Setting interval between checks to 0 seconds
Generated /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/extra-parts/oem.img
[35mDone packing /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/extra-parts/oem.img[0m
[35mPreparing partiton userdata[0m
[36mMerging /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/extra-parts/userdata/normal into /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/extra-parts/userdata[0m
[35mPacking /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/extra-parts/userdata.img from /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/extra-parts/userdata.fs[0m
Using host tools in /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/latest/host (except for mke2fs)
Making /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/extra-parts/userdata.img from /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/extra-parts/userdata (auto sized)
Start from 8M for ext4.
Making /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/extra-parts/userdata.img from /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/extra-parts/userdata with size(8192KB)
mke2fs 1.47.0 (5-Feb-2023)
Creating regular file /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/extra-parts/userdata.img
Creating filesystem with 2048 4k blocks and 2048 inodes

Allocating group tables: 0/1   done                            
Writing inode tables: 0/1   done                            
Creating journal (1024 blocks): done
Copying files into the device: done
Writing superblocks and filesystem accounting information: 0/1   done

tune2fs 1.47.0 (5-Feb-2023)
Setting maximal mount count to -1
Setting interval between checks to 0 seconds
Generated /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/extra-parts/userdata.img
[35mDone packing /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/extra-parts/userdata.img[0m
[35mRunning mk-extra-parts.sh - build_extra_part succeeded.[0m
[35mPacked files:[0m
MiniLoaderAll.bin(/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/u-boot/rv1126bp_spl_loader_v1.03.103.bin): 433K
boot.img(/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/kernel-6.1/boot.img): 13M
misc.img(/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/misc.img): 48K
oem.img(/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/extra-parts/oem.img): 18M
parameter.txt(/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/.chips/rv1126b/parameter.txt): 541
recovery.img(/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/recovery/ramboot.img): 19M
rootfs.img(/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/images/rootfs.ext2): 186M
uboot.img(/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/u-boot/uboot.img): 4.0M
userdata.img(/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/extra-parts/userdata.img): 8.0M
[35mMaking update image...[0m
[36m==========================================[0m
[36m          Start packing update image[0m
[36m==========================================[0m
[35mGenerating package-file for update:[0m
# NAME	PATH
package-file	package-file
parameter	parameter.txt
bootloader	MiniLoaderAll.bin
uboot	uboot.img
misc	misc.img
boot	boot.img
recovery	recovery.img
backup	RESERVED
rootfs	rootfs.img
oem	oem.img
userdata	userdata.img
[35mPacking /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/firmware/update.img for update...[0m
Android Firmware Package Tool v2.29
------ PACKAGE ------
Add file: ./package-file
package-file,Add file: ./package-file done,offset=0x800,size=0xe1,userspace=0x1
Add file: ./parameter.txt
parameter,Add file: ./parameter.txt done,offset=0x1000,size=0x229,userspace=0x1,flash_address=0x00000000
Add file: ./MiniLoaderAll.bin
bootloader,Add file: ./MiniLoaderAll.bin done,offset=0x1800,size=0x6c1c0,userspace=0xd9
Add file: ./uboot.img
uboot,Add file: ./uboot.img done,offset=0x6e000,size=0x400000,userspace=0x800,flash_address=0x00004000
Add file: ./misc.img
misc,Add file: ./misc.img done,offset=0x46e000,size=0xc000,userspace=0x18,flash_address=0x00006000
Add file: ./boot.img
boot,Add file: ./boot.img done,offset=0x47a000,size=0xcbc200,userspace=0x1979,flash_address=0x00008000
Add file: ./recovery.img
recovery,Add file: ./recovery.img done,offset=0x1136800,size=0x121ec00,userspace=0x243e,flash_address=0x00028000
Add file: ./rootfs.img
rootfs,Add file: ./rootfs.img done,offset=0x2355800,size=0xb912000,userspace=0x17224,flash_address=0x00078000
Add file: ./oem.img
oem,Add file: ./oem.img done,offset=0xdc67800,size=0x1156000,userspace=0x22ac,flash_address=0x00c78000
Add file: ./userdata.img
userdata,Add file: ./userdata.img done,offset=0xedbd800,size=0x800000,userspace=0x1000,flash_address=0x00cb8000
Add CRC...
Make firmware OK!
------ OK ------
********rkImageMaker ver 2.29********
Generating new image, please wait...
Writing head info...
Writing boot file...
Writing firmware...
Generating MD5 data...
MD5 data generated successfully!
New image generated successfully!
[35m
Run 'make edit-package-file' if you want to change the package-file.
[0m
[35mRunning mk-updateimg.sh - build_updateimg succeeded.[0m
[36mImages under /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/firmware/ are ready![0m
[35mRunning mk-firmware.sh - build_firmware succeeded.[0m
[35mRunning 99-all.sh - build_all succeeded.[0m

# 2025-08-30 08:50:37
2025-08-30T08:50:37 make: Entering directory '/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot'
2025-08-30T08:50:42 /usr/bin/make -j1  O=/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc HOSTCC="/usr/bin/gcc" HOSTCXX="/usr/bin/g++" syncconfig
2025-08-30T08:50:42 make[1]: Entering directory '/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot'
2025-08-30T08:50:42 make[1]: Leaving directory '/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot'
2025-08-30T08:50:42 [7m>>>   Finalizing host directory[27m
2025-08-30T08:50:42 [7m>>>   Finalizing target directory[27m
2025-08-30T08:50:42 mkdir -p /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/host/etc/meson
2025-08-30T08:50:42 sed -e "s%@TARGET_CFLAGS@%'-D_LARGEFILE_SOURCE', '-D_LARGEFILE64_SOURCE', '-D_FILE_OFFSET_BITS=64', '-O2', '-g0', '-D_FORTIFY_SOURCE=1'@PKG_TARGET_CFLAGS@%g" -e "s%@TARGET_LDFLAGS@%@PKG_TARGET_LDFLAGS@%g" -e "s%@TARGET_CXXFLAGS@%'-D_LARGEFILE_SOURCE', '-D_LARGEFILE64_SOURCE', '-D_FILE_OFFSET_BITS=64', '-O2', '-g0', '-D_FORTIFY_SOURCE=1'@PKG_TARGET_CXXFLAGS@%g" -e "s%@TARGET_FCFLAGS@%'-O2', '-g0'@PKG_TARGET_FCFLAGS@%g"         -e "s%@TARGET_CC@%/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/host/bin/aarch64-buildroot-linux-gnu-gcc%g" -e "s%@TARGET_CXX@%/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/host/bin/aarch64-buildroot-linux-gnu-g++%g" -e "s%@TARGET_AR@%/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/host/bin/aarch64-buildroot-linux-gnu-gcc-ar%g" -e "s%@TARGET_STRIP@%/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/host/bin/aarch64-buildroot-linux-gnu-strip%g"         -e "s%@TARGET_FC@%/bin/false%g" -e "s%@TARGET_ARCH@%aarch64%g" -e "s%@TARGET_CPU@%cortex-a53%g" -e "s%@TARGET_ENDIAN@%little%g" -e "s%@TARGET_FCFLAGS@%%g" -e "s%@TARGET_CFLAGS@%%g" -e "s%@TARGET_LDFLAGS@%%g" -e "s%@TARGET_CXXFLAGS@%%g" -e "s%@BR2_CMAKE@%/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/host/bin/cmake%g" -e "s%@PKGCONF_HOST_BINARY@%/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/host/bin/pkgconf%g" -e "s%@HOST_DIR@%/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/host%g" -e "s%@STAGING_DIR@%/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/host/aarch64-buildroot-linux-gnu/sysroot%g" -e "s%@STATIC@%false%g" /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/support/misc/cross-compilation.conf.in > /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/host/etc/meson/cross-compilation.conf.in
2025-08-30T08:50:42 sed         -e "s%@TARGET_CC@%/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/host/bin/aarch64-buildroot-linux-gnu-gcc%g" -e "s%@TARGET_CXX@%/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/host/bin/aarch64-buildroot-linux-gnu-g++%g" -e "s%@TARGET_AR@%/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/host/bin/aarch64-buildroot-linux-gnu-gcc-ar%g" -e "s%@TARGET_STRIP@%/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/host/bin/aarch64-buildroot-linux-gnu-strip%g"         -e "s%@TARGET_FC@%/bin/false%g" -e "s%@TARGET_ARCH@%aarch64%g" -e "s%@TARGET_CPU@%cortex-a53%g" -e "s%@TARGET_ENDIAN@%little%g" -e "s%@TARGET_FCFLAGS@%'-O2', '-g0'%g" -e "s%@TARGET_CFLAGS@%'-D_LARGEFILE_SOURCE', '-D_LARGEFILE64_SOURCE', '-D_FILE_OFFSET_BITS=64', '-O2', '-g0', '-D_FORTIFY_SOURCE=1'%g" -e "s%@TARGET_LDFLAGS@%%g" -e "s%@TARGET_CXXFLAGS@%'-D_LARGEFILE_SOURCE', '-D_LARGEFILE64_SOURCE', '-D_FILE_OFFSET_BITS=64', '-O2', '-g0', '-D_FORTIFY_SOURCE=1'%g" -e "s%@BR2_CMAKE@%/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/host/bin/cmake%g" -e "s%@PKGCONF_HOST_BINARY@%/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/host/bin/pkgconf%g" -e "s%@HOST_DIR@%/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/host%g" -e "s%@STAGING_DIR@%/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/host/aarch64-buildroot-linux-gnu/sysroot%g" -e "s%@STATIC@%false%g" /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/support/misc/cross-compilation.conf.in > /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/host/etc/meson/cross-compilation.conf
2025-08-30T08:50:42 grep -qsE '^/bin/bash$' /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target/etc/shells || echo "/bin/bash" >> /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target/etc/shells
2025-08-30T08:50:42 /usr/bin/sed -i -e '/# GENERIC_SERIAL$/s~^.*#~::respawn:-/bin/sh # ttyS0::respawn:/sbin/getty -L ttyS0 115200 vt100 #~' /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target/etc/inittab
2025-08-30T08:50:42 /usr/bin/sed -i -e '/^#.*-o remount,rw \/$/s~^#\+~~' /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target/etc/inittab
2025-08-30T08:50:42 if grep -q CONFIG_ASH=y /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/build/busybox-1.36.1/.config; then grep -qsE '^/bin/ash$' /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target/etc/shells || echo "/bin/ash" >> /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target/etc/shells; fi
2025-08-30T08:50:42 if grep -q CONFIG_HUSH=y /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/build/busybox-1.36.1/.config; then grep -qsE '^/bin/hush$' /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target/etc/shells || echo "/bin/hush" >> /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target/etc/shells; fi
2025-08-30T08:50:42 /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/host/bin/udevadm hwdb --update --root /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target
2025-08-30T08:50:43 rm -f /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target/usr/share/glib-2.0/schemas/*.xml /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target/usr/share/glib-2.0/schemas/*.dtd
2025-08-30T08:50:43 /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/host/bin/glib-compile-schemas /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/host/aarch64-buildroot-linux-gnu/sysroot/usr/share/glib-2.0/schemas --targetdir=/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target/usr/share/glib-2.0/schemas
2025-08-30T08:50:43 No schema files found: doing nothing.
2025-08-30T08:50:43 mkdir -p /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target/etc
2025-08-30T08:50:43 echo "rv1126b" > /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target/etc/hostname
2025-08-30T08:50:43 /usr/bin/sed -i -e '$a \*********\trv1126b' -e '/^*********/d' /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target/etc/hosts
2025-08-30T08:50:43 mkdir -p /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target/etc
2025-08-30T08:50:43 echo "Welcome to RV1126B Buildroot" > /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target/etc/issue
2025-08-30T08:50:43 /usr/bin/sed -i -e s,^root:[^:]*:,root:"`/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/host/bin/mkpasswd -m "sha-256" "rockchip"`":, /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target/etc/shadow
2025-08-30T08:50:43 grep -qsE '^/bin/sh$' /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target/etc/shells || echo "/bin/sh" >> /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target/etc/shells
2025-08-30T08:50:43 ln -sf bash /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target/bin/sh
2025-08-30T08:50:43 /usr/bin/sed -i -e '/^root:/s,[^/]*$,bash,' /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target/etc/passwd
2025-08-30T08:50:43 if [ -x /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target/sbin/swapon -a -x /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target/sbin/swapoff ]; then /usr/bin/sed -i -e '/^#.*\/sbin\/swap/s/^#\+[[:blank:]]*//' /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target/etc/inittab; else /usr/bin/sed -i -e '/^[^#].*\/sbin\/swap/s/^/#/' /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target/etc/inittab; fi
2025-08-30T08:50:43 printf '%s\n' C en_US locale-archive > /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/build/locales.nopurge
2025-08-30T08:50:43 for dir in /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target/usr/share/locale /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target/usr/share/X11/locale /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target/usr/lib/locale; do if [ ! -d $dir ]; then continue; fi; for langdir in $dir/*; do if [ -e "${langdir}" ]; then grep -qx "${langdir##*/}" /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/build/locales.nopurge || rm -rf $langdir; fi done; done
2025-08-30T08:50:43 if [ -d /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target/usr/share/X11/locale ]; then for lang in C en_US; do if [ -f /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target/usr/share/X11/locale/$lang/XLC_LOCALE ]; then echo "$lang/XLC_LOCALE: $lang"; fi done > /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target/usr/share/X11/locale/locale.dir; fi
2025-08-30T08:50:43 rm -rf /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target/usr/share/aclocal \
2025-08-30T08:50:43 /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target/usr/lib/cmake /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target/usr/share/cmake \
2025-08-30T08:50:43 /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target/usr/lib/rpm /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target/usr/doc
2025-08-30T08:50:43 find /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target/usr/{lib,share}/ -name '*.cmake' -print0 | xargs -0 rm -f
2025-08-30T08:50:43 find /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target/lib/ /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target/usr/lib/ /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target/usr/libexec/ \
2025-08-30T08:50:43 \( -name '*.la' -o -name '*.prl' \) -print0 | xargs -0 rm -f
2025-08-30T08:50:43 rm -rf /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target/usr/include \
2025-08-30T08:50:43 /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target/usr/lib/pkgconfig /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target/usr/share/pkgconfig
2025-08-30T08:50:43 find /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target/lib/ /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target/usr/lib/ /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target/usr/libexec/ \
2025-08-30T08:50:43 -name '*.a' -print0 | xargs -0 rm -f
2025-08-30T08:50:43 rm -rf /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target/usr/share/gdb
2025-08-30T08:50:43 rm -rf /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target/usr/share/zsh
2025-08-30T08:50:43 rm -rf /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target/usr/man /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target/usr/share/man
2025-08-30T08:50:43 rm -rf /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target/usr/info /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target/usr/share/info
2025-08-30T08:50:43 rm -rf /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target/usr/doc /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target/usr/share/doc
2025-08-30T08:50:43 rm -rf /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target/usr/share/gtk-doc
2025-08-30T08:50:43 rmdir /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target/usr/share 2>/dev/null || true
2025-08-30T08:50:43 rm -rf /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target/lib/debug /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target/usr/lib/debug
2025-08-30T08:50:43 find /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target   -type f \( -perm /111 -o -name '*.so*' \) -not \( -name 'libpthread*.so*' -o -name 'ld-*.so*' -o -name '*.ko' \) -print0 | xargs -0 /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/host/bin/aarch64-buildroot-linux-gnu-strip --remove-section=.comment --remove-section=.note 2>/dev/null || true
2025-08-30T08:50:43 find /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target   \( -name 'ld-*.so*' -o -name 'libpthread*.so*' \) -print0 | xargs -0 -r /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/host/bin/aarch64-buildroot-linux-gnu-strip --remove-section=.comment --remove-section=.note --strip-debug 2>/dev/null || true
2025-08-30T08:50:43 test -f /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target/etc/ld.so.conf && \
2025-08-30T08:50:43 { echo "ERROR: we shouldn't have a /etc/ld.so.conf file"; exit 1; } || true
2025-08-30T08:50:43 test -d /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target/etc/ld.so.conf.d && \
2025-08-30T08:50:43 { echo "ERROR: we shouldn't have a /etc/ld.so.conf.d directory"; exit 1; } || true
2025-08-30T08:50:43 mkdir -p /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target/etc
2025-08-30T08:50:43 ( \
2025-08-30T08:50:43 echo "NAME=Buildroot"; \
2025-08-30T08:50:43 echo "VERSION=2024.02"; \
2025-08-30T08:50:43 echo "ID=buildroot"; \
2025-08-30T08:50:43 echo "VERSION_ID=2024.02"; \
2025-08-30T08:50:43 echo "PRETTY_NAME=\"Buildroot 2024.02\"" \
2025-08-30T08:50:43 ) >  /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target/usr/lib/os-release
2025-08-30T08:50:43 ln -sf ../usr/lib/os-release /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target/etc
2025-08-30T08:50:43 [7m>>>   Sanitizing RPATH in target tree[27m
2025-08-30T08:50:43 PARALLEL_JOBS=23 \
2025-08-30T08:50:43 PER_PACKAGE_DIR=/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/per-package \
2025-08-30T08:50:43 /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/support/scripts/fix-rpath target
2025-08-30T08:50:45 [7m>>>   Sanity check in overlay board/rockchip/common/base[27m
2025-08-30T08:50:45 [7m>>>   Sanity check in overlay board/rockchip/rv1126b/fs-overlay-ipc/[27m
2025-08-30T08:50:45 [7m>>>   Copying overlay board/rockchip/common/base[27m
2025-08-30T08:50:45 [7m>>>   Copying overlay board/rockchip/rv1126b/fs-overlay-ipc/[27m
2025-08-30T08:50:45 [7m>>>   Executing post-build script board/rockchip/common/post-build.sh[27m
2025-08-30T08:50:45 >>> Ignored board/rockchip/common/overlays/10-weston
2025-08-30T08:50:45 >>> Ignored board/rockchip/common/overlays/11-weston-chromium
2025-08-30T08:50:45 >>> Copying board/rockchip/common/overlays/20-wlan0
2025-08-30T08:50:45 sending incremental file list
2025-08-30T08:50:45 etc/
2025-08-30T08:50:45 etc/network/
2025-08-30T08:50:45 etc/network/if-down.d/
2025-08-30T08:50:45 etc/network/if-pre-up.d/
2025-08-30T08:50:45 etc/network/if-up.d/
2025-08-30T08:50:45 
2025-08-30T08:50:45 sent 293 bytes  received 33 bytes  652.00 bytes/sec
2025-08-30T08:50:45 total size is 1,457  speedup is 4.47
2025-08-30T08:50:45 >>> Copying board/rockchip/common/overlays/default
2025-08-30T08:50:45 sending incremental file list
2025-08-30T08:50:45 
2025-08-30T08:50:45 sent 46 bytes  received 12 bytes  116.00 bytes/sec
2025-08-30T08:50:45 total size is 0  speedup is 0.00
2025-08-30T08:50:45 Executing post-build.sh...
2025-08-30T08:50:45 [35m
2025-08-30T08:50:45 ############### Rockchip Linux SDK ###############
2025-08-30T08:50:45 [0m
2025-08-30T08:50:45 [35mManifest: rv1126b_linux6.1_release_v1.0.0_20250620.xml[0m
2025-08-30T08:50:45 
2025-08-30T08:50:45 [35mLog colors: [0m[36mmessage [0m[35mnotice [0m[34mwarning [0m[91merror [0m[31mfatal[0m
2025-08-30T08:50:45 
2025-08-30T08:50:45 >>> [35mRunning within sudo(root) environment![0m
2025-08-30T08:50:45 
2025-08-30T08:50:45 [35mUsing kernel version(6.1) from environment[0m
2025-08-30T08:50:45 [35mUsing rootfs system(buildroot) from environment[0m
2025-08-30T08:50:45 >>> [35mRunning 00-wifibt.sh for /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target (buildroot init=busybox)...[0m
2025-08-30T08:50:45 >>> [36mBuilding Wifi/BT module and firmwares...[0m
2025-08-30T08:50:45 [35mSkipping 00-wifibt.sh - build_wifibt for missing configs:  RK_WIFIBT RK_WIFIBT_MODULES.[0m
2025-08-30T08:50:45 >>> [35mRunning 01-hostname.sh for /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target (buildroot init=busybox)...[0m
2025-08-30T08:50:45 [36mSetting hostname: rv1126bp-buildroot[0m
2025-08-30T08:50:45 >>> [35mRunning 10-os-release.sh for /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target (buildroot init=busybox)...[0m
2025-08-30T08:50:45 [36mAdding information to /etc/os-release...[0m
2025-08-30T08:50:45 >>> [35mRunning 20-info.sh for /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target (buildroot init=busybox)...[0m
2025-08-30T08:50:45 >>> [35mRunning 30-fstab.sh for /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target (buildroot init=busybox)...[0m
2025-08-30T08:50:45 [36mFixing up /etc/fstab...[0m
2025-08-30T08:50:45 [36mFixing up rootfs type: ext4[0m
2025-08-30T08:50:45 [36mFixup basic partitions for non-systemd init...[0m
2025-08-30T08:50:45 [36mFixing up basic partition: proc /proc[0m
2025-08-30T08:50:45 [36mFixing up basic partition: devtmpfs /dev[0m
2025-08-30T08:50:45 [36mFixing up basic partition: devpts /dev/pts mode=0620,ptmxmode=0000,gid=5[0m
2025-08-30T08:50:45 [36mFixing up basic partition: tmpfs /dev/shm nosuid,nodev,noexec[0m
2025-08-30T08:50:45 [36mFixing up basic partition: sysfs /sys nosuid,nodev,noexec[0m
2025-08-30T08:50:45 [36mFixing up basic partition: configfs /sys/kernel/config[0m
2025-08-30T08:50:45 [36mFixing up basic partition: debugfs /sys/kernel/debug[0m
2025-08-30T08:50:45 [36mFixing up basic partition: pstore /sys/fs/pstore nosuid,nodev,noexec[0m
2025-08-30T08:50:45 [36mFixing up device partition: PARTLABEL=oem /oem ext4 defaults[0m
2025-08-30T08:50:45 [36mFixing up device partition: PARTLABEL=userdata /userdata ext4 defaults[0m
2025-08-30T08:50:45 >>> [35mRunning 40-busybox-reboot.sh for /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target (buildroot init=busybox)...[0m
2025-08-30T08:50:45 >>> [35mRunning 50-locale.sh for /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target (buildroot init=busybox)...[0m
2025-08-30T08:50:45 [36mSetting LANG environment to en_US.UTF-8...[0m
2025-08-30T08:50:45 >>> [35mRunning 90-overlay.sh for /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target (buildroot init=busybox)...[0m
2025-08-30T08:50:45 >>> [35mHandling overlay: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/overlays/common/default)...[0m
2025-08-30T08:50:45 sending incremental file list
2025-08-30T08:50:45 ./
2025-08-30T08:50:45 
2025-08-30T08:50:45 sent 53 bytes  received 19 bytes  144.00 bytes/sec
2025-08-30T08:50:45 total size is 0  speedup is 0.00
2025-08-30T08:50:45 >>> [35mInstalling overlay: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/overlays/common/udev-rules to /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target...[0m
2025-08-30T08:50:45 sending incremental file list
2025-08-30T08:50:45 lib/
2025-08-30T08:50:45 lib/udev/
2025-08-30T08:50:45 lib/udev/rules.d/
2025-08-30T08:50:46 
2025-08-30T08:50:46 sent 229 bytes  received 24 bytes  506.00 bytes/sec
2025-08-30T08:50:46 total size is 1,854  speedup is 7.33
2025-08-30T08:50:46 >>> [35mHandling overlay: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/overlays/rootfs/async-commit)...[0m
2025-08-30T08:50:46 >>> [35mHandling overlay: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/overlays/rootfs/bootanim)...[0m
2025-08-30T08:50:46 >>> [35mHandling overlay: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/overlays/rootfs/chromium)...[0m
2025-08-30T08:50:46 >>> [35mInstalling overlay: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/overlays/rootfs/default to /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target...[0m
2025-08-30T08:50:46 sending incremental file list
2025-08-30T08:50:46 ./
2025-08-30T08:50:46 
2025-08-30T08:50:46 sent 49 bytes  received 19 bytes  136.00 bytes/sec
2025-08-30T08:50:46 total size is 0  speedup is 0.00
2025-08-30T08:50:46 >>> [35mHandling overlay: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/overlays/rootfs/disk-helpers)...[0m
2025-08-30T08:50:46 >>> [36mInstalling mount service...[0m
2025-08-30T08:50:46 sending incremental file list
2025-08-30T08:50:46 usr/
2025-08-30T08:50:46 usr/bin/
2025-08-30T08:50:46 
2025-08-30T08:50:46 sent 158 bytes  received 24 bytes  364.00 bytes/sec
2025-08-30T08:50:46 total size is 13,953  speedup is 76.66
2025-08-30T08:50:46 >>> [35mHandling overlay: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/fonts)...[0m
2025-08-30T08:50:46 [35mNo extra fonts for buildroot by default[0m
2025-08-30T08:50:46 >>> [35mHandling overlay: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/overlays/rootfs/frecon)...[0m
2025-08-30T08:50:46 >>> [35mHandling overlay: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/overlays/rootfs/fstrim)...[0m
2025-08-30T08:50:46 >>> [35mHandling overlay: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/overlays/rootfs/generate-logs)...[0m
2025-08-30T08:50:46 >>> [35mHandling overlay: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/overlays/rootfs/input-event-daemon)...[0m
2025-08-30T08:50:46 [35mDisabling input-event-daemon...[0m
2025-08-30T08:50:46 >>> [35mHandling overlay: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/overlays/rootfs/irqbalance)...[0m
2025-08-30T08:50:46 >>> [35mHandling overlay: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/overlays/rootfs/log-guardian)...[0m
2025-08-30T08:50:46 >>> [35mHandling overlay: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/tools)...[0m
2025-08-30T08:50:46 >>> [35mHandling overlay: /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/overlays/rootfs/usb-gadget)...[0m
2025-08-30T08:50:46 >>> [36mInstalling USB gadget service...[0m
2025-08-30T08:50:46 [36mUSB gadget functions: adb[0m
2025-08-30T08:50:46 >>> [36mInstalling ADBD...[0m
2025-08-30T08:50:46 sending incremental file list
2025-08-30T08:50:46 lib/
2025-08-30T08:50:46 lib/udev/
2025-08-30T08:50:46 lib/udev/rules.d/
2025-08-30T08:50:46 usr/bin/
2025-08-30T08:50:46 usr/bin/usbdevice
2025-08-30T08:50:46 
2025-08-30T08:50:46 sent 17,391 bytes  received 52 bytes  34,886.00 bytes/sec
2025-08-30T08:50:46 total size is 17,268  speedup is 0.99
2025-08-30T08:50:46 >>> [35mRunning 91-modules.sh for /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target (buildroot init=busybox)...[0m
2025-08-30T08:50:46 [36mStrip kernel modules...[0m
2025-08-30T08:50:46 copy from `/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target/usr/lib/module/rockit.ko' [elf64-littleaarch64] to `/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target/usr/lib/module/stkeg49P' [elf64-littleaarch64]
2025-08-30T08:50:46 copy from `/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target/usr/lib/module/kmpp.ko' [elf64-littleaarch64] to `/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target/usr/lib/module/st4xbH0R' [elf64-littleaarch64]
2025-08-30T08:50:46 >>> [35mRunning 95-extra-parts.sh for /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target (buildroot init=busybox)...[0m
2025-08-30T08:50:46 [36mPreparing extra partitions...[0m
2025-08-30T08:50:46 >>> [35mRunning 97-ldcache.sh for /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target (buildroot init=busybox)...[0m
2025-08-30T08:50:46 >>> [35mRunning 99-owner.sh for /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target (buildroot init=busybox)...[0m
2025-08-30T08:50:46 [36mFixing up owner for /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output...[0m
2025-08-30T08:50:46 changed ownership of '/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/misc.img' from root:root to 1000:1000
2025-08-30T08:50:46 changed ownership of '/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/.make_usage' from root:root to 1000:1000
2025-08-30T08:50:46 changed ownership of '/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/.parsed_cmds' from root:root to 1000:1000
2025-08-30T08:50:46 changed ownership of '/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/rootfs' from root:root to 1000:1000
2025-08-30T08:50:46 changed ownership of '/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/firmware' from root:root to 1000:1000
2025-08-30T08:50:46 changed ownership of '/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/firmware/misc.img' from root:root to 1000:1000
2025-08-30T08:50:46 changed ownership of '/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/firmware/uboot.img' from root:root to 1000:1000
2025-08-30T08:50:46 changed ownership of '/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/firmware/MiniLoaderAll.bin' from root:root to 1000:1000
2025-08-30T08:50:46 changed ownership of '/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/firmware/boot.img' from root:root to 1000:1000
2025-08-30T08:50:46 changed ownership of '/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/final.env' from root:root to 1000:1000
2025-08-30T08:50:46 changed ownership of '/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/extra-parts/userdata-mount' from root:root to 1000:1000
2025-08-30T08:50:46 changed ownership of '/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/extra-parts/oem-mount' from root:root to 1000:1000
2025-08-30T08:50:46 changed ownership of '/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/initial.env' from root:root to 1000:1000
2025-08-30T08:50:46 changed ownership of '/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/log' from root:root to 1000:1000
2025-08-30T08:50:46 changed ownership of '/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/buildroot' from root:root to 1000:1000
2025-08-30T08:50:46 changed ownership of '/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/buildroot/.stamp_build_start' from root:root to 1000:1000
2025-08-30T08:50:46 changed ownership of '/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/buildroot/images' from root:root to 1000:1000
2025-08-30T08:50:46 changed ownership of '/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/sessions/2025-08-30_08-50-17' from root:root to 1000:1000
2025-08-30T08:50:46 changed ownership of '/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/sessions/2025-08-30_08-50-17/part-table' from root:root to 1000:1000
2025-08-30T08:50:46 changed ownership of '/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/sessions/2025-08-30_08-50-17/.config' from root:root to 1000:1000
2025-08-30T08:50:46 changed ownership of '/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/sessions/2025-08-30_08-50-17/br-rockchip_rv1126b_ipc.log' from root:root to 1000:1000
2025-08-30T08:50:46 changed ownership of '/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/sessions/2025-08-30_08-50-17/post-rootfs_2025-08-30_08-50-45.log' from root:root to 1000:1000
2025-08-30T08:50:46 changed ownership of '/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/sessions/2025-08-30_08-50-17/build_2025-08-30_08-50-18.log' from root:root to 1000:1000
2025-08-30T08:50:46 changed ownership of '/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/sessions/2025-08-30_08-50-17/final.env' from root:root to 1000:1000
2025-08-30T08:50:46 changed ownership of '/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/sessions/2025-08-30_08-50-17/post-rootfs.log' from root:root to 1000:1000
2025-08-30T08:50:46 changed ownership of '/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/sessions/2025-08-30_08-50-17/.stamp_post_start' from root:root to 1000:1000
2025-08-30T08:50:46 changed ownership of '/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/sessions/2025-08-30_08-50-17/99-all-build.log' from root:root to 1000:1000
2025-08-30T08:50:46 changed ownership of '/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/sessions/2025-08-30_08-50-17/br-rootfs.log' from root:root to 1000:1000
2025-08-30T08:50:46 changed ownership of '/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/sessions/2025-08-30_08-50-17/initial.env' from root:root to 1000:1000
2025-08-30T08:50:46 changed ownership of '/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/sessions/2025-08-30_08-50-17/br.log' from root:root to 1000:1000
2025-08-30T08:50:46 changed ownership of '/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/sessions/2025-08-30_08-50-17/99-all-build_2025-08-30_08-50-18.log' from root:root to 1000:1000
2025-08-30T08:50:46 changed ownership of '/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/sessions/2025-08-30_08-50-17/br-rockchip_rv1126b_ipc_2025-08-30_08-50-37.log' from root:root to 1000:1000
2025-08-30T08:50:46 changed ownership of '/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/sessions/2025-08-30_08-50-17/build.log' from root:root to 1000:1000
2025-08-30T08:50:46 changed ownership of '/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/sessions/latest' from root:root to 1000:1000
2025-08-30T08:50:46 >>> [35mRunning build.sh - post-rootfs succeeded.[0m
2025-08-30T08:50:46 [35mFiles changed in post-rootfs stage:[0m
2025-08-30T08:50:46 ./etc/hosts
2025-08-30T08:50:46 ./etc/fstab
2025-08-30T08:50:46 ./etc/os-release
2025-08-30T08:50:46 ./etc/init.d/S50usbdevice.sh
2025-08-30T08:50:46 ./etc/init.d/S00mountall.sh
2025-08-30T08:50:46 ./etc/hostname
2025-08-30T08:50:46 ./etc/profile.d/usbdevice.sh
2025-08-30T08:50:46 ./etc/profile.d/adbd.sh
2025-08-30T08:50:46 ./etc/profile.d/lang.sh
2025-08-30T08:50:46 ./lib
2025-08-30T08:50:46 ./usr/lib/module/rockit.ko
2025-08-30T08:50:46 ./usr/lib/module/kmpp.ko
2025-08-30T08:50:46 ./usr/bin/logcat
2025-08-30T08:50:46 ./usr/bin/usbdevice
2025-08-30T08:50:46 touch /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target/usr
2025-08-30T08:50:46 [7m>>>   Generating root filesystems common tables[27m
2025-08-30T08:50:46 rm -rf /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/build/buildroot-fs
2025-08-30T08:50:46 mkdir -p /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/build/buildroot-fs
2025-08-30T08:50:46 printf '   	chrony -1 chrony -1 * /run/chrony - - Time daemon\n 	dbus -1 dbus -1 * /run/dbus - dbus DBus messagebus user\n 	dhcpcd -1 dhcpcd -1 * - - - dhcpcd user\n 	ntp -1 ntp -1 * - - - ntpd user\n 	- - input -1 * - - - Input device group\n	- - kvm -1 * - - - kvm nodes\n	- - sgx -1 * - - - SGX device nodes\n\n' >> /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/build/buildroot-fs/full_users_table.txt
2025-08-30T08:50:46 printf '   	/bin/busybox                     f 4755 0  0 - - - - -\n 	/var/lib/chrony d 755 chrony chrony - - - - -\n 	/usr/libexec/dbus-daemon-launch-helper f 4750 0 dbus - - - - -\n 	/var/lib/nginx d 755 33 33 - - - - -\n\n' > /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/build/buildroot-fs/full_devices_table.txt
2025-08-30T08:50:46 cat system/device_table.txt >> /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/build/buildroot-fs/full_devices_table.txt
2025-08-30T08:50:46 [7m>>>   Generating filesystem image rootfs.cpio[27m
2025-08-30T08:50:46 mkdir -p /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/images
2025-08-30T08:50:46 rm -rf /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/build/buildroot-fs/cpio
2025-08-30T08:50:46 mkdir -p /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/build/buildroot-fs/cpio
2025-08-30T08:50:46 rsync -auH --exclude=/THIS_IS_NOT_YOUR_ROOT_FILESYSTEM /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target/ /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/build/buildroot-fs/cpio/target
2025-08-30T08:50:46 echo '#!/bin/sh' > /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/build/buildroot-fs/cpio/fakeroot
2025-08-30T08:50:46 echo "set -e" >> /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/build/buildroot-fs/cpio/fakeroot
2025-08-30T08:50:46 echo "chown -h -R 0:0 /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/build/buildroot-fs/cpio/target" >> /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/build/buildroot-fs/cpio/fakeroot
2025-08-30T08:50:46 PATH="/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/host/bin:/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/host/sbin:/root/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin" /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/support/scripts/mkusers /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/build/buildroot-fs/full_users_table.txt /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/build/buildroot-fs/cpio/target >> /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/build/buildroot-fs/cpio/fakeroot
2025-08-30T08:50:47 echo "/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/host/bin/makedevs -d /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/build/buildroot-fs/full_devices_table.txt /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/build/buildroot-fs/cpio/target" >> /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/build/buildroot-fs/cpio/fakeroot
2025-08-30T08:50:47 printf '   	rm -rf /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/build/buildroot-fs/cpio/target/usr/lib/udev/hwdb.d/ /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/build/buildroot-fs/cpio/target/etc/udev/hwdb.d/\n' >> /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/build/buildroot-fs/cpio/fakeroot
2025-08-30T08:50:47 printf '   	if [ ! -e /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/build/buildroot-fs/cpio/target/init ]; then /usr/bin/install -m 0755 fs/cpio/init /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/build/buildroot-fs/cpio/target/init; fi\n	mkdir -p /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/build/buildroot-fs/cpio/target/dev\n	mknod -m 0622 /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/build/buildroot-fs/cpio/target/dev/console c 5 1\n' >> /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/build/buildroot-fs/cpio/fakeroot
2025-08-30T08:50:47 echo "find /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/build/buildroot-fs/cpio/target/run/ -mindepth 1 -prune -print0 | xargs -0r rm -rf --" >> /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/build/buildroot-fs/cpio/fakeroot
2025-08-30T08:50:47 echo "find /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/build/buildroot-fs/cpio/target/tmp/ -mindepth 1 -prune -print0 | xargs -0r rm -rf --" >> /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/build/buildroot-fs/cpio/fakeroot
2025-08-30T08:50:47 printf '   \n' >> /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/build/buildroot-fs/cpio/fakeroot
2025-08-30T08:50:47 printf '   \n' >> /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/build/buildroot-fs/cpio/fakeroot
2025-08-30T08:50:47 printf '   	cd /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/build/buildroot-fs/cpio/target && find . | LC_ALL=C sort | cpio  --quiet -o -H newc > /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/images/rootfs.cpio\n' >> /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/build/buildroot-fs/cpio/fakeroot
2025-08-30T08:50:47 chmod a+x /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/build/buildroot-fs/cpio/fakeroot
2025-08-30T08:50:47 PATH="/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/host/bin:/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/host/sbin:/root/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin" FAKEROOTDONTTRYCHOWN=1 /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/host/bin/fakeroot -- /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/build/buildroot-fs/cpio/fakeroot
2025-08-30T08:50:47 rootdir=/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/build/buildroot-fs/cpio/target
2025-08-30T08:50:47 table='/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/build/buildroot-fs/full_devices_table.txt'
2025-08-30T08:50:47 PATH="/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/host/bin:/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/host/sbin:/root/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin" gzip -9 -c -n /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/images/rootfs.cpio > /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/images/rootfs.cpio.gz
2025-08-30T08:51:01 [7m>>>   Generating filesystem image rootfs.ext2[27m
2025-08-30T08:51:01 mkdir -p /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/images
2025-08-30T08:51:01 rm -rf /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/build/buildroot-fs/ext2
2025-08-30T08:51:01 mkdir -p /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/build/buildroot-fs/ext2
2025-08-30T08:51:01 rsync -auH --exclude=/THIS_IS_NOT_YOUR_ROOT_FILESYSTEM /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target/ /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/build/buildroot-fs/ext2/target
2025-08-30T08:51:01 echo '#!/bin/sh' > /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/build/buildroot-fs/ext2/fakeroot
2025-08-30T08:51:01 echo "set -e" >> /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/build/buildroot-fs/ext2/fakeroot
2025-08-30T08:51:01 echo "chown -h -R 0:0 /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/build/buildroot-fs/ext2/target" >> /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/build/buildroot-fs/ext2/fakeroot
2025-08-30T08:51:01 PATH="/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/host/bin:/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/host/sbin:/root/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin" /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/support/scripts/mkusers /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/build/buildroot-fs/full_users_table.txt /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/build/buildroot-fs/ext2/target >> /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/build/buildroot-fs/ext2/fakeroot
2025-08-30T08:51:01 echo "/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/host/bin/makedevs -d /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/build/buildroot-fs/full_devices_table.txt /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/build/buildroot-fs/ext2/target" >> /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/build/buildroot-fs/ext2/fakeroot
2025-08-30T08:51:01 printf '   	rm -rf /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/build/buildroot-fs/ext2/target/usr/lib/udev/hwdb.d/ /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/build/buildroot-fs/ext2/target/etc/udev/hwdb.d/\n' >> /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/build/buildroot-fs/ext2/fakeroot
2025-08-30T08:51:01 echo "find /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/build/buildroot-fs/ext2/target/run/ -mindepth 1 -prune -print0 | xargs -0r rm -rf --" >> /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/build/buildroot-fs/ext2/fakeroot
2025-08-30T08:51:01 echo "find /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/build/buildroot-fs/ext2/target/tmp/ -mindepth 1 -prune -print0 | xargs -0r rm -rf --" >> /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/build/buildroot-fs/ext2/fakeroot
2025-08-30T08:51:01 printf '   \n' >> /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/build/buildroot-fs/ext2/fakeroot
2025-08-30T08:51:01 printf '   \n' >> /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/build/buildroot-fs/ext2/fakeroot
2025-08-30T08:51:01 printf '   	rm -f /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/images/rootfs.ext2\n	FILE_SIZE="$(du --apparent-size -sm /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/build/buildroot-fs/ext2/target | cut -f1)"\n	ALIGN_SIZE="$(($(find /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/build/buildroot-fs/ext2/target | wc -l) * 4096 / 1024 / 1024))"\n	ROOTFS_SIZE="$(( ($FILE_SIZE + $ALIGN_SIZE) * 110 / 100 + 64 ))"\n	/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/host/sbin/mkfs.ext4 -d /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/build/buildroot-fs/ext2/target -r 1 -b 4096 -N 0 -m 5 -L "rootfs" -I 256 -O ^64bit /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/images/rootfs.ext2 "${ROOTFS_SIZE}M"\n	/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/host/sbin/resize2fs -M /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/images/rootfs.ext2\n	/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/host/sbin/e2fsck -fy /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/images/rootfs.ext2\n' >> /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/build/buildroot-fs/ext2/fakeroot
2025-08-30T08:51:01 chmod a+x /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/build/buildroot-fs/ext2/fakeroot
2025-08-30T08:51:01 PATH="/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/host/bin:/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/host/sbin:/root/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin" FAKEROOTDONTTRYCHOWN=1 /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/host/bin/fakeroot -- /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/build/buildroot-fs/ext2/fakeroot
2025-08-30T08:51:01 rootdir=/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/build/buildroot-fs/ext2/target
2025-08-30T08:51:01 table='/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/build/buildroot-fs/full_devices_table.txt'
2025-08-30T08:51:02 mke2fs 1.47.0 (5-Feb-2023)
2025-08-30T08:51:02 Creating regular file /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/images/rootfs.ext2
2025-08-30T08:51:02 64-bit filesystem support is not enabled.  The larger fields afforded by this feature enable full-strength checksumming.  Pass -O 64bit to rectify.
2025-08-30T08:51:02 Creating filesystem with 59392 4k blocks and 59392 inodes
2025-08-30T08:51:02 Filesystem UUID: a87ade38-73cd-4b9b-a33e-3021a9226017
2025-08-30T08:51:02 Superblock backups stored on blocks:
2025-08-30T08:51:02 32768
2025-08-30T08:51:02 
2025-08-30T08:51:02 Allocating group tables: 0/2   done
2025-08-30T08:51:02 Writing inode tables: 0/2   done
2025-08-30T08:51:02 Creating journal (4096 blocks): done
2025-08-30T08:51:02 Copying files into the device: done
2025-08-30T08:51:02 Writing superblocks and filesystem accounting information: 0/2   done
2025-08-30T08:51:02 
2025-08-30T08:51:02 resize2fs 1.47.0 (5-Feb-2023)
2025-08-30T08:51:02 Resizing the filesystem on /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/images/rootfs.ext2 to 47378 (4k) blocks.
2025-08-30T08:51:02 The filesystem on /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/images/rootfs.ext2 is now 47378 (4k) blocks long.
2025-08-30T08:51:02 
2025-08-30T08:51:02 e2fsck 1.47.0 (5-Feb-2023)
2025-08-30T08:51:02 Pass 1: Checking inodes, blocks, and sizes
2025-08-30T08:51:02 Pass 2: Checking directory structure
2025-08-30T08:51:02 Pass 3: Checking directory connectivity
2025-08-30T08:51:02 Pass 4: Checking reference counts
2025-08-30T08:51:02 Pass 5: Checking group summary information
2025-08-30T08:51:02 rootfs: 2590/59392 files (0.1% non-contiguous), 45480/47378 blocks
2025-08-30T08:51:02 ln -sf rootfs.ext2 /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/images/rootfs.ext4
2025-08-30T08:51:02 [7m>>>   Generating filesystem image rootfs.squashfs[27m
2025-08-30T08:51:02 mkdir -p /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/images
2025-08-30T08:51:02 rm -rf /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/build/buildroot-fs/squashfs
2025-08-30T08:51:02 mkdir -p /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/build/buildroot-fs/squashfs
2025-08-30T08:51:02 rsync -auH --exclude=/THIS_IS_NOT_YOUR_ROOT_FILESYSTEM /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target/ /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/build/buildroot-fs/squashfs/target
2025-08-30T08:51:03 echo '#!/bin/sh' > /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/build/buildroot-fs/squashfs/fakeroot
2025-08-30T08:51:03 echo "set -e" >> /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/build/buildroot-fs/squashfs/fakeroot
2025-08-30T08:51:03 echo "chown -h -R 0:0 /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/build/buildroot-fs/squashfs/target" >> /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/build/buildroot-fs/squashfs/fakeroot
2025-08-30T08:51:03 PATH="/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/host/bin:/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/host/sbin:/root/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin" /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/support/scripts/mkusers /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/build/buildroot-fs/full_users_table.txt /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/build/buildroot-fs/squashfs/target >> /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/build/buildroot-fs/squashfs/fakeroot
2025-08-30T08:51:03 echo "/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/host/bin/makedevs -d /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/build/buildroot-fs/full_devices_table.txt /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/build/buildroot-fs/squashfs/target" >> /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/build/buildroot-fs/squashfs/fakeroot
2025-08-30T08:51:03 printf '   	rm -rf /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/build/buildroot-fs/squashfs/target/usr/lib/udev/hwdb.d/ /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/build/buildroot-fs/squashfs/target/etc/udev/hwdb.d/\n' >> /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/build/buildroot-fs/squashfs/fakeroot
2025-08-30T08:51:03 echo "find /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/build/buildroot-fs/squashfs/target/run/ -mindepth 1 -prune -print0 | xargs -0r rm -rf --" >> /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/build/buildroot-fs/squashfs/fakeroot
2025-08-30T08:51:03 echo "find /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/build/buildroot-fs/squashfs/target/tmp/ -mindepth 1 -prune -print0 | xargs -0r rm -rf --" >> /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/build/buildroot-fs/squashfs/fakeroot
2025-08-30T08:51:03 printf '   \n' >> /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/build/buildroot-fs/squashfs/fakeroot
2025-08-30T08:51:03 printf '   \n' >> /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/build/buildroot-fs/squashfs/fakeroot
2025-08-30T08:51:03 printf '   	/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/host/bin/mksquashfs /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/build/buildroot-fs/squashfs/target /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/images/rootfs.squashfs -noappend -processors 23 -b 128K  -comp gzip\n' >> /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/build/buildroot-fs/squashfs/fakeroot
2025-08-30T08:51:03 chmod a+x /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/build/buildroot-fs/squashfs/fakeroot
2025-08-30T08:51:03 PATH="/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/host/bin:/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/host/sbin:/root/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin" FAKEROOTDONTTRYCHOWN=1 /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/host/bin/fakeroot -- /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/build/buildroot-fs/squashfs/fakeroot
2025-08-30T08:51:03 rootdir=/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/build/buildroot-fs/squashfs/target
2025-08-30T08:51:03 table='/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/build/buildroot-fs/full_devices_table.txt'
2025-08-30T08:51:04 Parallel mksquashfs: Using 23 processors
2025-08-30T08:51:04 Creating 4.0 filesystem on /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/images/rootfs.squashfs, block size 131072.
2025-08-30T08:51:04 
[=============================================================-] 2766/2766 100%
2025-08-30T08:51:04 
2025-08-30T08:51:04 Exportable Squashfs 4.0 filesystem, gzip compressed, data block size 131072
2025-08-30T08:51:04 compressed data, compressed metadata, compressed fragments,
2025-08-30T08:51:04 compressed xattrs, compressed ids
2025-08-30T08:51:04 duplicates are removed
2025-08-30T08:51:04 Filesystem size 59127.75 Kbytes (57.74 Mbytes)
2025-08-30T08:51:04 40.71% of uncompressed filesystem size (145248.51 Kbytes)
2025-08-30T08:51:04 Inode table size 28068 bytes (27.41 Kbytes)
2025-08-30T08:51:04 29.10% of uncompressed inode table size (96461 bytes)
2025-08-30T08:51:04 Directory table size 31804 bytes (31.06 Kbytes)
2025-08-30T08:51:04 51.73% of uncompressed directory table size (61477 bytes)
2025-08-30T08:51:04 Number of duplicate files found 19
2025-08-30T08:51:04 Number of inodes 2579
2025-08-30T08:51:04 Number of files 1782
2025-08-30T08:51:04 Number of fragments 116
2025-08-30T08:51:04 Number of symbolic links 560
2025-08-30T08:51:04 Number of device nodes 0
2025-08-30T08:51:04 Number of fifo nodes 0
2025-08-30T08:51:04 Number of socket nodes 0
2025-08-30T08:51:04 Number of directories 237
2025-08-30T08:51:04 Number of hard-links 492
2025-08-30T08:51:04 Number of ids (unique uids + gids) 5
2025-08-30T08:51:04 Number of uids 3
2025-08-30T08:51:04 root (0)
2025-08-30T08:51:04 systemd-network (100)
2025-08-30T08:51:04 www-data (33)
2025-08-30T08:51:04 Number of gids 4
2025-08-30T08:51:04 root (0)
2025-08-30T08:51:04 systemd-network (102)
2025-08-30T08:51:04 systemd-journal (101)
2025-08-30T08:51:04 www-data (33)
2025-08-30T08:51:05 [7m>>>   Generating filesystem image rootfs.tar[27m
2025-08-30T08:51:05 mkdir -p /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/images
2025-08-30T08:51:05 rm -rf /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/build/buildroot-fs/tar
2025-08-30T08:51:05 mkdir -p /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/build/buildroot-fs/tar
2025-08-30T08:51:05 rsync -auH --exclude=/THIS_IS_NOT_YOUR_ROOT_FILESYSTEM /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/target/ /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/build/buildroot-fs/tar/target
2025-08-30T08:51:05 echo '#!/bin/sh' > /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/build/buildroot-fs/tar/fakeroot
2025-08-30T08:51:05 echo "set -e" >> /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/build/buildroot-fs/tar/fakeroot
2025-08-30T08:51:05 echo "chown -h -R 0:0 /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/build/buildroot-fs/tar/target" >> /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/build/buildroot-fs/tar/fakeroot
2025-08-30T08:51:05 PATH="/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/host/bin:/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/host/sbin:/root/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin" /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/support/scripts/mkusers /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/build/buildroot-fs/full_users_table.txt /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/build/buildroot-fs/tar/target >> /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/build/buildroot-fs/tar/fakeroot
2025-08-30T08:51:05 echo "/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/host/bin/makedevs -d /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/build/buildroot-fs/full_devices_table.txt /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/build/buildroot-fs/tar/target" >> /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/build/buildroot-fs/tar/fakeroot
2025-08-30T08:51:05 printf '   	rm -rf /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/build/buildroot-fs/tar/target/usr/lib/udev/hwdb.d/ /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/build/buildroot-fs/tar/target/etc/udev/hwdb.d/\n' >> /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/build/buildroot-fs/tar/fakeroot
2025-08-30T08:51:05 echo "find /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/build/buildroot-fs/tar/target/run/ -mindepth 1 -prune -print0 | xargs -0r rm -rf --" >> /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/build/buildroot-fs/tar/fakeroot
2025-08-30T08:51:05 echo "find /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/build/buildroot-fs/tar/target/tmp/ -mindepth 1 -prune -print0 | xargs -0r rm -rf --" >> /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/build/buildroot-fs/tar/fakeroot
2025-08-30T08:51:05 printf '   \n' >> /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/build/buildroot-fs/tar/fakeroot
2025-08-30T08:51:05 printf '   \n' >> /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/build/buildroot-fs/tar/fakeroot
2025-08-30T08:51:05 printf '   	(cd /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/build/buildroot-fs/tar/target; find -print0 | LC_ALL=C sort -z | tar  --pax-option=exthdr.name=%%d/PaxHeaders/%%f,atime:=0,ctime:=0 -cf /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/images/rootfs.tar --null --xattrs-include='\''*'\'' --no-recursion -T - --numeric-owner)\n' >> /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/build/buildroot-fs/tar/fakeroot
2025-08-30T08:51:05 chmod a+x /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/build/buildroot-fs/tar/fakeroot
2025-08-30T08:51:05 PATH="/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/host/bin:/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/host/sbin:/root/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin" FAKEROOTDONTTRYCHOWN=1 /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/host/bin/fakeroot -- /work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/build/buildroot-fs/tar/fakeroot
2025-08-30T08:51:05 rootdir=/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/build/buildroot-fs/tar/target
2025-08-30T08:51:05 table='/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot/output/rockchip_rv1126b_ipc/build/buildroot-fs/full_devices_table.txt'
2025-08-30T08:51:06 make: Leaving directory '/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/buildroot'

#!/bin/sh -e
find "/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/extra-parts/userdata" -user 1000 			-exec chown -ch 0:0 {} \;
"/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/device/rockchip/common/scripts/mk-image.sh" 			-t "ext4" -s "auto" -l "userdata" 			"/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/extra-parts/userdata" "/work/rv1126BP/rv1126b_linux6.1_release_v1.0.0/output/extra-parts/userdata.img"
